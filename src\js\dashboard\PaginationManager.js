/**
 * Gestionnaire de pagination pour les listeners temps-réel
 * Optimisation des performances avec startAfter et lazy loading
 */

class PaginationManager {
  constructor() {
    this.pageStates = new Map();
    this.loadingStates = new Map();
    this.hasMoreData = new Map();
    
    // Configuration par défaut
    this.defaultPageSize = 20;
    this.maxCacheSize = 100; // Maximum d'éléments en cache par tableau
  }

  /**
   * Initialiser la pagination pour un tableau
   */
  initializePagination(tableId, pageSize = this.defaultPageSize) {
    this.pageStates.set(tableId, {
      currentPage: 1,
      pageSize,
      lastDoc: null,
      totalItems: 0,
      cachedItems: [],
      isInitialized: false
    });
    
    this.loadingStates.set(tableId, false);
    this.hasMoreData.set(tableId, true);
    
    console.log(`📄 Pagination initialisée pour ${tableId} (taille: ${pageSize})`);
  }

  /**
   * Charger la page suivante avec startAfter
   */
  async loadNextPage(tableId, queryBuilder) {
    if (this.loadingStates.get(tableId)) {
      console.log(`⏳ Chargement déjà en cours pour ${tableId}`);
      return;
    }

    if (!this.hasMoreData.get(tableId)) {
      console.log(`📄 Pas de données supplémentaires pour ${tableId}`);
      return;
    }

    const pageState = this.pageStates.get(tableId);
    if (!pageState) {
      console.error(`❌ État de pagination non trouvé pour ${tableId}`);
      return;
    }

    this.loadingStates.set(tableId, true);
    this.showLoadingIndicator(tableId);

    try {
      console.log(`📊 Chargement page ${pageState.currentPage + 1} pour ${tableId}`);

      // Construire la requête avec pagination
      let query = queryBuilder();
      
      // Ajouter startAfter si ce n'est pas la première page
      if (pageState.lastDoc) {
        query = query.startAfter(pageState.lastDoc);
      }
      
      query = query.limit(pageState.pageSize);

      // Exécuter la requête
      const snapshot = await query.get();
      
      if (snapshot.empty) {
        this.hasMoreData.set(tableId, false);
        console.log(`📄 Fin des données atteinte pour ${tableId}`);
        return;
      }

      // Traiter les résultats
      const newItems = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        _doc: doc // Garder la référence pour startAfter
      }));

      // Mettre à jour l'état de pagination
      pageState.currentPage++;
      pageState.lastDoc = snapshot.docs[snapshot.docs.length - 1];
      pageState.totalItems += newItems.length;
      
      // Ajouter au cache avec limitation de taille
      pageState.cachedItems.push(...newItems);
      this.limitCacheSize(tableId);

      // Marquer comme initialisé
      pageState.isInitialized = true;

      // Vérifier s'il y a encore des données
      if (snapshot.docs.length < pageState.pageSize) {
        this.hasMoreData.set(tableId, false);
      }

      console.log(`✅ Page chargée pour ${tableId}: ${newItems.length} éléments`);
      
      // Déclencher la mise à jour de l'UI
      this.triggerUIUpdate(tableId, newItems, false);

    } catch (error) {
      console.error(`❌ Erreur lors du chargement de la page pour ${tableId}:`, error);
      this.showError(tableId, 'Erreur de chargement');
    } finally {
      this.loadingStates.set(tableId, false);
      this.hideLoadingIndicator(tableId);
    }
  }

  /**
   * Recharger complètement un tableau
   */
  async reloadTable(tableId, queryBuilder) {
    console.log(`🔄 Rechargement complet de ${tableId}`);
    
    // Réinitialiser l'état
    const pageState = this.pageStates.get(tableId);
    if (pageState) {
      pageState.currentPage = 1;
      pageState.lastDoc = null;
      pageState.totalItems = 0;
      pageState.cachedItems = [];
      pageState.isInitialized = false;
    }
    
    this.hasMoreData.set(tableId, true);
    
    // Charger la première page
    await this.loadNextPage(tableId, queryBuilder);
  }

  /**
   * Gérer la mise à jour temps-réel avec pagination
   */
  handleRealtimeUpdate(tableId, snapshot, queryBuilder) {
    const pageState = this.pageStates.get(tableId);
    if (!pageState || !pageState.isInitialized) {
      // Première mise à jour, charger la première page
      this.loadNextPage(tableId, queryBuilder);
      return;
    }

    // Traiter les changements en temps-réel
    const changes = snapshot.docChanges();
    let hasSignificantChanges = false;

    changes.forEach(change => {
      const docData = { id: change.doc.id, ...change.doc.data(), _doc: change.doc };
      
      switch (change.type) {
        case 'added':
          // Ajouter au début du cache (données les plus récentes)
          pageState.cachedItems.unshift(docData);
          pageState.totalItems++;
          hasSignificantChanges = true;
          break;
          
        case 'modified':
          // Mettre à jour l'élément existant
          const modifiedIndex = pageState.cachedItems.findIndex(item => item.id === docData.id);
          if (modifiedIndex !== -1) {
            pageState.cachedItems[modifiedIndex] = docData;
            hasSignificantChanges = true;
          }
          break;
          
        case 'removed':
          // Supprimer du cache
          const removedIndex = pageState.cachedItems.findIndex(item => item.id === docData.id);
          if (removedIndex !== -1) {
            pageState.cachedItems.splice(removedIndex, 1);
            pageState.totalItems--;
            hasSignificantChanges = true;
          }
          break;
      }
    });

    if (hasSignificantChanges) {
      // Limiter la taille du cache
      this.limitCacheSize(tableId);
      
      // Déclencher la mise à jour de l'UI avec les données en cache
      const visibleItems = pageState.cachedItems.slice(0, pageState.pageSize);
      this.triggerUIUpdate(tableId, visibleItems, true);
      
      console.log(`🔄 Mise à jour temps-réel pour ${tableId}: ${changes.length} changements`);
    }
  }

  /**
   * Limiter la taille du cache pour éviter la surcharge mémoire
   */
  limitCacheSize(tableId) {
    const pageState = this.pageStates.get(tableId);
    if (!pageState) return;

    if (pageState.cachedItems.length > this.maxCacheSize) {
      // Garder les éléments les plus récents
      pageState.cachedItems = pageState.cachedItems.slice(0, this.maxCacheSize);
      console.log(`🗑️ Cache limité à ${this.maxCacheSize} éléments pour ${tableId}`);
    }
  }

  /**
   * Obtenir les éléments visibles pour un tableau
   */
  getVisibleItems(tableId) {
    const pageState = this.pageStates.get(tableId);
    if (!pageState) return [];

    return pageState.cachedItems.slice(0, pageState.pageSize);
  }

  /**
   * Vérifier s'il y a plus de données à charger
   */
  hasMore(tableId) {
    return this.hasMoreData.get(tableId) || false;
  }

  /**
   * Obtenir les statistiques de pagination
   */
  getStats(tableId) {
    const pageState = this.pageStates.get(tableId);
    if (!pageState) return null;

    return {
      currentPage: pageState.currentPage,
      pageSize: pageState.pageSize,
      totalItems: pageState.totalItems,
      cachedItems: pageState.cachedItems.length,
      hasMore: this.hasMore(tableId),
      isLoading: this.loadingStates.get(tableId) || false
    };
  }

  /**
   * Déclencher la mise à jour de l'UI
   */
  triggerUIUpdate(tableId, items, isRealtimeUpdate) {
    // Émettre un événement personnalisé pour la mise à jour de l'UI
    const event = new CustomEvent('dashboardTableUpdate', {
      detail: {
        tableId,
        items,
        isRealtimeUpdate,
        stats: this.getStats(tableId)
      }
    });
    
    document.dispatchEvent(event);
  }

  /**
   * Afficher l'indicateur de chargement
   */
  showLoadingIndicator(tableId) {
    const container = document.getElementById(`${tableId}-table`);
    if (!container) return;

    let loadingIndicator = container.querySelector('.loading-indicator');
    if (!loadingIndicator) {
      loadingIndicator = document.createElement('div');
      loadingIndicator.className = 'loading-indicator';
      loadingIndicator.innerHTML = `
        <div class="loading-spinner">
          <div class="spinner"></div>
          <span>Chargement...</span>
        </div>
      `;
      container.appendChild(loadingIndicator);
    }
    
    loadingIndicator.style.display = 'block';
  }

  /**
   * Masquer l'indicateur de chargement
   */
  hideLoadingIndicator(tableId) {
    const container = document.getElementById(`${tableId}-table`);
    if (!container) return;

    const loadingIndicator = container.querySelector('.loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.style.display = 'none';
    }
  }

  /**
   * Afficher une erreur
   */
  showError(tableId, message) {
    const container = document.getElementById(`${tableId}-table`);
    if (!container) return;

    const errorDiv = document.createElement('div');
    errorDiv.className = 'pagination-error alert alert-error';
    errorDiv.innerHTML = `
      <span>❌ ${message}</span>
      <button onclick="paginationManager.reloadTable('${tableId}')">🔄 Réessayer</button>
    `;
    
    container.appendChild(errorDiv);
    
    // Supprimer après 5 secondes
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.parentNode.removeChild(errorDiv);
      }
    }, 5000);
  }

  /**
   * Nettoyer les états de pagination
   */
  cleanup() {
    console.log('🧹 Nettoyage de la pagination...');
    
    this.pageStates.clear();
    this.loadingStates.clear();
    this.hasMoreData.clear();
    
    console.log('✅ Pagination nettoyée');
  }

  /**
   * Créer un bouton "Charger plus"
   */
  createLoadMoreButton(tableId, queryBuilder) {
    const button = document.createElement('button');
    button.className = 'btn btn-outline load-more-btn';
    button.innerHTML = '📄 Charger plus';
    
    button.addEventListener('click', () => {
      this.loadNextPage(tableId, queryBuilder);
    });
    
    // Masquer si pas de données supplémentaires
    if (!this.hasMore(tableId)) {
      button.style.display = 'none';
    }
    
    return button;
  }
}

// Export pour utilisation globale
window.PaginationManager = PaginationManager;
