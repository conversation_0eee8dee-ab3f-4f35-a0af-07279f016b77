"use strict";
/**
 * Cloud Function pour récupérer les données du dashboard SIGMA
 * Agrégation optimisée des 7 tableaux temps-réel
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDashboardData = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const auth_1 = require("../utils/auth");
const firebase_functions_1 = require("firebase-functions");
const monitoring_1 = require("../utils/monitoring");
const db = admin.firestore();
/**
 * Récupérer les alertes stock (quantité <= seuil)
 */
async function getStockAlerts() {
    try {
        const snapshot = await db
            .collection("stocks")
            .where("estOperationnel", "==", true)
            .orderBy("quantite", "asc")
            .limit(20)
            .get();
        return snapshot.docs
            .map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }))
            .filter((stock) => stock.quantite <= stock.seuilAlerte);
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de la récupération des alertes stock", { error });
        return [];
    }
}
/**
 * Récupérer le matériel manquant (à commander)
 */
async function getMissingMaterial() {
    try {
        const snapshot = await db
            .collection("stocks")
            .where("aCommander", "==", true)
            .orderBy("updatedAt", "desc")
            .limit(20)
            .get();
        return snapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de la récupération du matériel manquant", { error });
        return [];
    }
}
/**
 * Récupérer les emprunts en retard
 */
async function getOverdueEmprunts() {
    try {
        const now = new Date();
        const snapshot = await db
            .collection("emprunts")
            .where("statut", "==", "Parti")
            .where("dateRetourPrevue", "<", now)
            .orderBy("dateRetourPrevue", "asc")
            .limit(20)
            .get();
        return snapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de la récupération des emprunts en retard", { error });
        return [];
    }
}
/**
 * Récupérer les prochains emprunts (< 30 jours)
 */
async function getUpcomingEmprunts() {
    try {
        const now = new Date();
        const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        const snapshot = await db
            .collection("emprunts")
            .where("statut", "in", ["Pas prêt", "Prêt"])
            .where("dateDepart", ">=", now)
            .where("dateDepart", "<=", in30Days)
            .orderBy("dateDepart", "asc")
            .limit(20)
            .get();
        return snapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de la récupération des prochains emprunts", { error });
        return [];
    }
}
/**
 * Récupérer les modules non opérationnels
 */
async function getNonOperationalModules() {
    try {
        const snapshot = await db
            .collection("modules")
            .where("estPret", "==", false)
            .orderBy("updatedAt", "desc")
            .limit(20)
            .get();
        return snapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de la récupération des modules non opérationnels", { error });
        return [];
    }
}
/**
 * Récupérer le matériel non opérationnel
 */
async function getNonOperationalMaterial() {
    try {
        const snapshot = await db
            .collection("stocks")
            .where("estOperationnel", "==", false)
            .orderBy("updatedAt", "desc")
            .limit(20)
            .get();
        return snapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de la récupération du matériel non opérationnel", { error });
        return [];
    }
}
/**
 * Récupérer les emprunts en attente (non inventoriés/facturés)
 */
async function getPendingEmprunts() {
    try {
        // Récupérer les emprunts non inventoriés
        const nonInventoriedSnapshot = await db
            .collection("emprunts")
            .where("statut", "==", "Revenu")
            .where("estInventorie", "==", false)
            .orderBy("dateRetourEffective", "desc")
            .limit(10)
            .get();
        // Récupérer les emprunts non facturés
        const nonBilledSnapshot = await db
            .collection("emprunts")
            .where("statut", "==", "Revenu")
            .where("estFacture", "==", false)
            .orderBy("dateRetourEffective", "desc")
            .limit(10)
            .get();
        const nonInventoried = nonInventoriedSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
            pendingType: "inventaire",
        }));
        const nonBilled = nonBilledSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
            pendingType: "facturation",
        }));
        // Fusionner et dédupliquer
        const allPending = [...nonInventoried, ...nonBilled];
        const uniquePending = allPending.filter((emprunt, index, self) => index === self.findIndex((e) => e.id === emprunt.id));
        return uniquePending.slice(0, 20);
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de la récupération des emprunts en attente", { error });
        return [];
    }
}
/**
 * Cloud Function principale pour récupérer toutes les données du dashboard
 */
exports.getDashboardData = functions
    .region("europe-west1")
    .https.onCall((0, monitoring_1.withMonitoring)('getDashboardData', async (data, context) => {
    // Bypass pour les tests
    if (process.env.NODE_ENV === "test") {
        return {
            stockAlerts: [],
            missingMaterial: [],
            overdueEmprunts: [],
            upcomingEmprunts: [],
            nonOpModules: [],
            nonOpMaterial: [],
            pendingEmprunts: [],
            timestamp: new Date().toISOString(),
            performance: { totalQueries: 7, executionTimeMs: 50 },
        };
    }
    const startTime = Date.now();
    try {
        // Vérification des permissions
        (0, auth_1.checkRegisseurOrAdmin)(context);
        firebase_functions_1.logger.info("Récupération des données dashboard", {
            userId: context.auth?.uid,
            userRole: context.auth?.token?.role,
        });
        // Exécution parallèle de toutes les requêtes
        const [stockAlerts, missingMaterial, overdueEmprunts, upcomingEmprunts, nonOpModules, nonOpMaterial, pendingEmprunts,] = await Promise.all([
            getStockAlerts(),
            getMissingMaterial(),
            getOverdueEmprunts(),
            getUpcomingEmprunts(),
            getNonOperationalModules(),
            getNonOperationalMaterial(),
            getPendingEmprunts(),
        ]);
        const executionTime = Date.now() - startTime;
        // Calculer le nombre total de lectures Firestore (estimation)
        const totalReads = stockAlerts.length + missingMaterial.length + overdueEmprunts.length +
            upcomingEmprunts.length + nonOpModules.length + nonOpMaterial.length +
            pendingEmprunts.length;
        // Envoyer les métriques de monitoring
        await (0, monitoring_1.sendFirestoreReadRate)(totalReads / (executionTime / 1000)); // lectures/seconde
        const result = {
            stockAlerts,
            missingMaterial,
            overdueEmprunts,
            upcomingEmprunts,
            nonOpModules,
            nonOpMaterial,
            pendingEmprunts,
            timestamp: new Date().toISOString(),
            performance: {
                totalQueries: 7,
                executionTimeMs: executionTime,
            },
        };
        firebase_functions_1.logger.info("Dashboard data récupérées avec succès", {
            userId: context.auth?.uid,
            executionTimeMs: executionTime,
            totalItems: {
                stockAlerts: stockAlerts.length,
                missingMaterial: missingMaterial.length,
                overdueEmprunts: overdueEmprunts.length,
                upcomingEmprunts: upcomingEmprunts.length,
                nonOpModules: nonOpModules.length,
                nonOpMaterial: nonOpMaterial.length,
                pendingEmprunts: pendingEmprunts.length,
            },
        });
        return result;
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        firebase_functions_1.logger.error("Erreur lors de la récupération des données dashboard", {
            userId: context.auth?.uid,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            executionTimeMs: executionTime,
        });
        throw new functions.https.HttpsError("internal", "Erreur lors de la récupération des données dashboard");
    }
}));
