/**
 * Interface d'administration des utilisateurs SIGMA
 * Gestion des utilisateurs, rôles et permissions
 */

class UserAdminManager {
    constructor() {
        this.auth = null;
        this.functions = null;
        this.currentUser = null;
        this.users = [];
        this.currentPage = 1;
        this.hasMore = false;
        this.lastUserId = null;
        this.searchTerm = '';
        this.roleFilter = '';
        
        // Éléments DOM
        this.elements = {
            // Navigation
            userNameSpan: document.getElementById('user-name'),
            logoutBtn: document.getElementById('logout-btn'),
            
            // Filtres
            searchInput: document.getElementById('search-input'),
            roleFilter: document.getElementById('role-filter'),
            refreshBtn: document.getElementById('refresh-btn'),
            
            // États
            loadingState: document.getElementById('loading-state'),
            messageContainer: document.getElementById('message-container'),
            
            // Table
            usersTable: document.getElementById('users-table'),
            usersTbody: document.getElementById('users-tbody'),
            usersCount: document.getElementById('users-count'),
            
            // Pagination
            pagination: document.getElementById('pagination'),
            prevPage: document.getElementById('prev-page'),
            nextPage: document.getElementById('next-page'),
            pageInfo: document.getElementById('page-info'),
            
            // Modals
            roleModal: document.getElementById('role-modal'),
            modalClose: document.getElementById('modal-close'),
            modalUserName: document.getElementById('modal-user-name'),
            modalUserEmail: document.getElementById('modal-user-email'),
            newRole: document.getElementById('new-role'),
            roleReason: document.getElementById('role-reason'),
            cancelRoleChange: document.getElementById('cancel-role-change'),
            confirmRoleChange: document.getElementById('confirm-role-change'),
            
            confirmModal: document.getElementById('confirm-modal'),
            confirmModalClose: document.getElementById('confirm-modal-close'),
            confirmMessage: document.getElementById('confirm-message'),
            confirmCancel: document.getElementById('confirm-cancel'),
            confirmOk: document.getElementById('confirm-ok')
        };
        
        this.selectedUserId = null;
        this.init();
    }

    /**
     * Initialisation
     */
    async init() {
        try {
            // Attendre Firebase
            await this.waitForFirebase();
            
            // Récupérer les services
            const services = window.firebaseServices;
            this.auth = services.auth;
            this.functions = services.functions;
            
            // Vérifier l'authentification
            this.auth.onAuthStateChanged(this.handleAuthStateChange.bind(this));
            
            // Configurer les écouteurs
            this.setupEventListeners();
            
            console.log('✅ UserAdminManager initialisé');
        } catch (error) {
            console.error('❌ Erreur initialisation UserAdminManager:', error);
            this.showMessage('Erreur d\'initialisation', 'error');
        }
    }

    /**
     * Attendre Firebase
     */
    waitForFirebase() {
        return new Promise((resolve, reject) => {
            const check = () => {
                if (window.firebaseServices) {
                    resolve();
                } else {
                    setTimeout(check, 100);
                }
            };
            check();
            setTimeout(() => reject(new Error('Firebase non disponible')), 10000);
        });
    }

    /**
     * Gestion des changements d'authentification
     */
    async handleAuthStateChange(user) {
        if (user) {
            this.currentUser = user;
            
            // Vérifier le rôle
            const idTokenResult = await user.getIdTokenResult();
            const role = idTokenResult.claims.role;
            
            if (role !== 'admin') {
                this.showMessage('Accès refusé. Seuls les administrateurs peuvent accéder à cette page.', 'error');
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
                return;
            }
            
            // Mettre à jour l'interface
            this.elements.userNameSpan.textContent = user.displayName || user.email;
            
            // Charger les utilisateurs
            await this.loadUsers();
        } else {
            // Rediriger vers la connexion
            window.location.href = '/html/login.html';
        }
    }

    /**
     * Configuration des écouteurs d'événements
     */
    setupEventListeners() {
        // Navigation
        this.elements.logoutBtn?.addEventListener('click', this.logout.bind(this));
        
        // Filtres
        this.elements.searchInput?.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        this.elements.roleFilter?.addEventListener('change', this.handleRoleFilter.bind(this));
        this.elements.refreshBtn?.addEventListener('click', this.refreshUsers.bind(this));
        
        // Pagination
        this.elements.prevPage?.addEventListener('click', this.previousPage.bind(this));
        this.elements.nextPage?.addEventListener('click', this.nextPage.bind(this));
        
        // Modals
        this.elements.modalClose?.addEventListener('click', this.closeRoleModal.bind(this));
        this.elements.cancelRoleChange?.addEventListener('click', this.closeRoleModal.bind(this));
        this.elements.confirmRoleChange?.addEventListener('click', this.confirmRoleChange.bind(this));
        
        this.elements.confirmModalClose?.addEventListener('click', this.closeConfirmModal.bind(this));
        this.elements.confirmCancel?.addEventListener('click', this.closeConfirmModal.bind(this));
        
        // Fermer les modals en cliquant à l'extérieur
        this.elements.roleModal?.addEventListener('click', (e) => {
            if (e.target === this.elements.roleModal) {
                this.closeRoleModal();
            }
        });
        
        this.elements.confirmModal?.addEventListener('click', (e) => {
            if (e.target === this.elements.confirmModal) {
                this.closeConfirmModal();
            }
        });
    }

    /**
     * Charger les utilisateurs
     */
    async loadUsers(reset = true) {
        try {
            if (reset) {
                this.currentPage = 1;
                this.lastUserId = null;
                this.users = [];
            }
            
            this.showLoading(true);
            
            const listUsersFunction = this.functions.httpsCallable('listUsers');
            const result = await listUsersFunction({
                limit: 20,
                startAfter: this.lastUserId,
                role: this.roleFilter || undefined,
                searchTerm: this.searchTerm || undefined
            });
            
            const data = result.data;
            
            if (reset) {
                this.users = data.users;
            } else {
                this.users.push(...data.users);
            }
            
            this.hasMore = data.hasMore;
            this.lastUserId = data.lastUserId;
            
            this.renderUsers();
            this.updatePagination();
            this.updateUsersCount();
            
        } catch (error) {
            console.error('❌ Erreur chargement utilisateurs:', error);
            this.showMessage('Erreur lors du chargement des utilisateurs', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Afficher les utilisateurs dans le tableau
     */
    renderUsers() {
        if (!this.elements.usersTbody) return;
        
        this.elements.usersTbody.innerHTML = '';
        
        this.users.forEach(user => {
            const row = this.createUserRow(user);
            this.elements.usersTbody.appendChild(row);
        });
    }

    /**
     * Créer une ligne utilisateur
     */
    createUserRow(user) {
        const row = document.createElement('tr');
        
        // Formatage des dates
        const lastActivity = user.lastActivityAt ? 
            new Date(user.lastActivityAt.seconds * 1000).toLocaleDateString('fr-FR') : 
            'Jamais';
        
        row.innerHTML = `
            <td>
                <div class="user-info">
                    <div class="user-avatar">
                        <span class="material-icons">person</span>
                    </div>
                    <div class="user-details">
                        <h4>${user.displayName || 'Sans nom'}</h4>
                        <p>${user.empruntsCount || 0} emprunts</p>
                    </div>
                </div>
            </td>
            <td>${user.email}</td>
            <td>
                <span class="role-badge role-${user.role}">
                    ${this.getRoleLabel(user.role)}
                </span>
            </td>
            <td>
                <span class="status-badge status-${user.isActive ? 'active' : 'inactive'}">
                    ${user.isActive ? 'Actif' : 'Inactif'}
                </span>
            </td>
            <td>${lastActivity}</td>
            <td>
                <div class="actions-cell">
                    <button class="btn btn-sm btn-primary" onclick="userAdmin.openRoleModal('${user.uid}')">
                        <span class="material-icons">edit</span>
                        Rôle
                    </button>
                </div>
            </td>
        `;
        
        return row;
    }

    /**
     * Obtenir le libellé du rôle
     */
    getRoleLabel(role) {
        const labels = {
            'admin': 'Administrateur',
            'regisseur': 'Régisseur',
            'utilisateur': 'Utilisateur'
        };
        return labels[role] || role;
    }

    /**
     * Ouvrir le modal de modification de rôle
     */
    async openRoleModal(userId) {
        try {
            this.selectedUserId = userId;
            
            // Récupérer les détails de l'utilisateur
            const getUserRoleFunction = this.functions.httpsCallable('getUserRole');
            const result = await getUserRoleFunction({ userId });
            const user = result.data;
            
            // Remplir le modal
            this.elements.modalUserName.textContent = user.displayName || 'Sans nom';
            this.elements.modalUserEmail.textContent = user.email;
            this.elements.newRole.value = user.role;
            this.elements.roleReason.value = '';
            
            // Afficher le modal
            this.elements.roleModal.style.display = 'flex';
            
        } catch (error) {
            console.error('❌ Erreur ouverture modal:', error);
            this.showMessage('Erreur lors de l\'ouverture du modal', 'error');
        }
    }

    /**
     * Fermer le modal de rôle
     */
    closeRoleModal() {
        this.elements.roleModal.style.display = 'none';
        this.selectedUserId = null;
    }

    /**
     * Confirmer le changement de rôle
     */
    async confirmRoleChange() {
        try {
            if (!this.selectedUserId) return;
            
            const newRole = this.elements.newRole.value;
            const reason = this.elements.roleReason.value;
            
            // Appeler la fonction de changement de rôle
            const setUserRoleFunction = this.functions.httpsCallable('setUserRole');
            await setUserRoleFunction({
                userId: this.selectedUserId,
                role: newRole,
                reason: reason || undefined
            });
            
            this.showMessage('Rôle modifié avec succès', 'success');
            this.closeRoleModal();
            
            // Recharger les utilisateurs
            await this.loadUsers();
            
        } catch (error) {
            console.error('❌ Erreur changement rôle:', error);
            this.showMessage('Erreur lors du changement de rôle', 'error');
        }
    }

    /**
     * Fermer le modal de confirmation
     */
    closeConfirmModal() {
        this.elements.confirmModal.style.display = 'none';
    }

    /**
     * Gestion de la recherche
     */
    handleSearch(event) {
        this.searchTerm = event.target.value.trim();
        this.loadUsers(true);
    }

    /**
     * Gestion du filtre de rôle
     */
    handleRoleFilter(event) {
        this.roleFilter = event.target.value;
        this.loadUsers(true);
    }

    /**
     * Actualiser les utilisateurs
     */
    async refreshUsers() {
        await this.loadUsers(true);
        this.showMessage('Liste des utilisateurs actualisée', 'success');
    }

    /**
     * Page précédente
     */
    async previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            // Pour la pagination précédente, on doit recharger depuis le début
            // C'est une limitation de Firestore
            await this.loadUsers(true);
        }
    }

    /**
     * Page suivante
     */
    async nextPage() {
        if (this.hasMore) {
            this.currentPage++;
            await this.loadUsers(false);
        }
    }

    /**
     * Mettre à jour la pagination
     */
    updatePagination() {
        if (!this.elements.pagination) return;
        
        this.elements.prevPage.disabled = this.currentPage <= 1;
        this.elements.nextPage.disabled = !this.hasMore;
        this.elements.pageInfo.textContent = `Page ${this.currentPage}`;
        
        this.elements.pagination.style.display = this.users.length > 0 ? 'flex' : 'none';
    }

    /**
     * Mettre à jour le compteur d'utilisateurs
     */
    updateUsersCount() {
        if (!this.elements.usersCount) return;
        
        const count = this.users.length;
        const text = count === 0 ? 'Aucun utilisateur' : 
                    count === 1 ? '1 utilisateur' : 
                    `${count} utilisateurs`;
        
        this.elements.usersCount.textContent = text;
    }

    /**
     * Afficher/masquer le chargement
     */
    showLoading(show) {
        if (this.elements.loadingState) {
            this.elements.loadingState.style.display = show ? 'block' : 'none';
        }
        if (this.elements.usersTable) {
            this.elements.usersTable.style.display = show ? 'none' : 'table';
        }
    }

    /**
     * Afficher un message
     */
    showMessage(text, type = 'info') {
        if (!this.elements.messageContainer) return;
        
        const message = document.createElement('div');
        message.className = `message message-${type}`;
        
        const icon = type === 'success' ? 'check_circle' : 
                    type === 'error' ? 'error' : 'info';
        
        message.innerHTML = `
            <span class="material-icons">${icon}</span>
            <span>${text}</span>
        `;
        
        this.elements.messageContainer.appendChild(message);
        
        // Supprimer après 5 secondes
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 5000);
    }

    /**
     * Déconnexion
     */
    async logout() {
        try {
            await this.auth.signOut();
        } catch (error) {
            console.error('❌ Erreur déconnexion:', error);
        }
    }

    /**
     * Fonction utilitaire de debounce
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialiser quand le DOM est prêt
document.addEventListener('DOMContentLoaded', () => {
    window.userAdmin = new UserAdminManager();
});
