/**
 * Script de configuration des alertes Cloud Monitoring pour SIGMA Dashboard
 * Automatise la création des alertes critiques et des notifications
 */

const { MonitoringClient } = require('@google-cloud/monitoring');
const admin = require('firebase-admin');

// Configuration
const PROJECT_ID = 'sigma-nova';
const NOTIFICATION_CHANNELS = {
  email: 'projects/sigma-nova/notificationChannels/email-alerts',
  slack: 'projects/sigma-nova/notificationChannels/slack-alerts'
};

// Initialiser les clients
const monitoring = new MonitoringClient();
const projectPath = monitoring.projectPath(PROJECT_ID);

/**
 * Configuration des alertes dashboard
 */
const ALERT_POLICIES = [
  {
    displayName: 'SIGMA Dashboard - Latence Élevée',
    documentation: {
      content: `
# Alerte Latence Dashboard SIGMA

Cette alerte se déclenche lorsque la latence des requêtes dashboard dépasse 500ms.

## Actions recommandées :
1. Vérifier les performances des Cloud Functions
2. Analyser les requêtes Firestore lentes
3. Vérifier la charge du système
4. Optimiser les index si nécessaire

## Seuils :
- **Warning** : > 500ms pendant 2 minutes
- **Critical** : > 1000ms pendant 1 minute
      `,
      mimeType: 'text/markdown'
    },
    conditions: [{
      displayName: 'Dashboard Query Latency',
      conditionThreshold: {
        filter: 'resource.type="cloud_function" AND resource.labels.function_name="getDashboardData"',
        comparison: 'COMPARISON_GREATER_THAN',
        thresholdValue: 500,
        duration: '120s',
        aggregations: [{
          alignmentPeriod: '60s',
          perSeriesAligner: 'ALIGN_MEAN',
          crossSeriesReducer: 'REDUCE_MEAN',
          groupByFields: ['resource.label.function_name']
        }]
      }
    }],
    alertStrategy: {
      autoClose: '1800s'
    },
    enabled: true,
    notificationChannels: [NOTIFICATION_CHANNELS.email]
  },
  
  {
    displayName: 'SIGMA Dashboard - Quota Firestore Dépassé',
    documentation: {
      content: `
# Alerte Quota Firestore SIGMA

Cette alerte se déclenche lorsque le nombre de lectures Firestore dépasse 100/seconde.

## Actions recommandées :
1. Analyser les requêtes les plus coûteuses
2. Vérifier la pagination des listeners
3. Optimiser les index composites
4. Réduire la fréquence des mises à jour si nécessaire

## Seuils :
- **Warning** : > 80 lectures/sec pendant 2 minutes
- **Critical** : > 100 lectures/sec pendant 1 minute
      `,
      mimeType: 'text/markdown'
    },
    conditions: [{
      displayName: 'Firestore Read Rate',
      conditionThreshold: {
        filter: 'resource.type="firestore_database"',
        comparison: 'COMPARISON_GREATER_THAN',
        thresholdValue: 100,
        duration: '60s',
        aggregations: [{
          alignmentPeriod: '60s',
          perSeriesAligner: 'ALIGN_RATE',
          crossSeriesReducer: 'REDUCE_SUM'
        }]
      }
    }],
    alertStrategy: {
      autoClose: '900s'
    },
    enabled: true,
    notificationChannels: [NOTIFICATION_CHANNELS.email, NOTIFICATION_CHANNELS.slack]
  },
  
  {
    displayName: 'SIGMA Dashboard - Taux d\'Erreur Élevé',
    documentation: {
      content: `
# Alerte Taux d'Erreur Dashboard SIGMA

Cette alerte se déclenche lorsque le taux d'erreur des Cloud Functions dépasse 5%.

## Actions recommandées :
1. Analyser les logs d'erreur des Cloud Functions
2. Vérifier la connectivité Firestore
3. Contrôler les permissions et l'authentification
4. Vérifier l'état des services Firebase

## Seuils :
- **Warning** : > 3% d'erreurs pendant 5 minutes
- **Critical** : > 5% d'erreurs pendant 3 minutes
      `,
      mimeType: 'text/markdown'
    },
    conditions: [{
      displayName: 'Dashboard Error Rate',
      conditionThreshold: {
        filter: 'resource.type="cloud_function" AND resource.labels.function_name=~".*dashboard.*"',
        comparison: 'COMPARISON_GREATER_THAN',
        thresholdValue: 0.05, // 5%
        duration: '300s',
        aggregations: [{
          alignmentPeriod: '60s',
          perSeriesAligner: 'ALIGN_RATE',
          crossSeriesReducer: 'REDUCE_MEAN'
        }]
      }
    }],
    alertStrategy: {
      autoClose: '1200s'
    },
    enabled: true,
    notificationChannels: [NOTIFICATION_CHANNELS.email, NOTIFICATION_CHANNELS.slack]
  },
  
  {
    displayName: 'SIGMA Dashboard - Alertes Stock Critiques',
    documentation: {
      content: `
# Alerte Stocks Critiques SIGMA

Cette alerte se déclenche lorsque plus de 10 articles sont en alerte stock critique.

## Actions recommandées :
1. Vérifier les stocks physiques
2. Lancer les commandes urgentes
3. Contacter les fournisseurs
4. Mettre à jour les seuils si nécessaire

## Seuils :
- **Warning** : > 5 alertes critiques
- **Critical** : > 10 alertes critiques
      `,
      mimeType: 'text/markdown'
    },
    conditions: [{
      displayName: 'Critical Stock Count',
      conditionThreshold: {
        filter: 'resource.type="cloud_function" AND metric.type="custom.googleapis.com/sigma/critical_stock_count"',
        comparison: 'COMPARISON_GREATER_THAN',
        thresholdValue: 10,
        duration: '0s',
        aggregations: [{
          alignmentPeriod: '300s',
          perSeriesAligner: 'ALIGN_MAX',
          crossSeriesReducer: 'REDUCE_MAX'
        }]
      }
    }],
    alertStrategy: {
      autoClose: '3600s'
    },
    enabled: true,
    notificationChannels: [NOTIFICATION_CHANNELS.email]
  },
  
  {
    displayName: 'SIGMA Dashboard - Emprunts en Retard Critiques',
    documentation: {
      content: `
# Alerte Emprunts en Retard SIGMA

Cette alerte se déclenche lorsque plus de 5 emprunts sont en retard critique (>7 jours).

## Actions recommandées :
1. Contacter les emprunteurs en retard
2. Envoyer des rappels automatiques
3. Escalader vers la direction si nécessaire
4. Vérifier les procédures de suivi

## Seuils :
- **Warning** : > 3 emprunts en retard critique
- **Critical** : > 5 emprunts en retard critique
      `,
      mimeType: 'text/markdown'
    },
    conditions: [{
      displayName: 'Critical Overdue Count',
      conditionThreshold: {
        filter: 'resource.type="cloud_function" AND metric.type="custom.googleapis.com/sigma/critical_overdue_count"',
        comparison: 'COMPARISON_GREATER_THAN',
        thresholdValue: 5,
        duration: '0s',
        aggregations: [{
          alignmentPeriod: '300s',
          perSeriesAligner: 'ALIGN_MAX',
          crossSeriesReducer: 'REDUCE_MAX'
        }]
      }
    }],
    alertStrategy: {
      autoClose: '3600s'
    },
    enabled: true,
    notificationChannels: [NOTIFICATION_CHANNELS.email]
  }
];

/**
 * Créer ou mettre à jour une politique d'alerte
 */
async function createOrUpdateAlertPolicy(policy) {
  try {
    console.log(`📊 Configuration de l'alerte: ${policy.displayName}`);
    
    // Vérifier si l'alerte existe déjà
    const [existingPolicies] = await monitoring.listAlertPolicies({
      name: projectPath,
      filter: `displayName="${policy.displayName}"`
    });
    
    if (existingPolicies.length > 0) {
      // Mettre à jour l'alerte existante
      const existingPolicy = existingPolicies[0];
      const updatedPolicy = {
        ...existingPolicy,
        ...policy,
        name: existingPolicy.name
      };
      
      const [result] = await monitoring.updateAlertPolicy({
        alertPolicy: updatedPolicy
      });
      
      console.log(`✅ Alerte mise à jour: ${result.displayName}`);
      return result;
    } else {
      // Créer une nouvelle alerte
      const [result] = await monitoring.createAlertPolicy({
        name: projectPath,
        alertPolicy: policy
      });
      
      console.log(`✅ Alerte créée: ${result.displayName}`);
      return result;
    }
    
  } catch (error) {
    console.error(`❌ Erreur lors de la configuration de l'alerte ${policy.displayName}:`, error);
    throw error;
  }
}

/**
 * Créer les métriques personnalisées
 */
async function createCustomMetrics() {
  console.log('📈 Création des métriques personnalisées...');
  
  const metrics = [
    {
      type: 'custom.googleapis.com/sigma/critical_stock_count',
      displayName: 'SIGMA Critical Stock Count',
      description: 'Nombre d\'articles en alerte stock critique',
      metricKind: 'GAUGE',
      valueType: 'INT64'
    },
    {
      type: 'custom.googleapis.com/sigma/critical_overdue_count',
      displayName: 'SIGMA Critical Overdue Count', 
      description: 'Nombre d\'emprunts en retard critique (>7 jours)',
      metricKind: 'GAUGE',
      valueType: 'INT64'
    },
    {
      type: 'custom.googleapis.com/sigma/dashboard_query_duration',
      displayName: 'SIGMA Dashboard Query Duration',
      description: 'Durée des requêtes dashboard en millisecondes',
      metricKind: 'GAUGE',
      valueType: 'DOUBLE'
    }
  ];
  
  for (const metric of metrics) {
    try {
      await monitoring.createMetricDescriptor({
        name: projectPath,
        metricDescriptor: metric
      });
      console.log(`✅ Métrique créée: ${metric.displayName}`);
    } catch (error) {
      if (error.code === 6) { // ALREADY_EXISTS
        console.log(`ℹ️ Métrique déjà existante: ${metric.displayName}`);
      } else {
        console.error(`❌ Erreur création métrique ${metric.displayName}:`, error);
      }
    }
  }
}

/**
 * Configurer les canaux de notification
 */
async function setupNotificationChannels() {
  console.log('📧 Configuration des canaux de notification...');
  
  const channels = [
    {
      type: 'email',
      displayName: 'SIGMA Dashboard Email Alerts',
      description: 'Alertes email pour le dashboard SIGMA',
      labels: {
        email_address: '<EMAIL>'
      },
      enabled: true
    },
    {
      type: 'slack',
      displayName: 'SIGMA Dashboard Slack Alerts',
      description: 'Alertes Slack pour le dashboard SIGMA',
      labels: {
        channel_name: '#sigma-alerts',
        url: 'https://hooks.slack.com/services/...' // À configurer
      },
      enabled: false // Désactivé par défaut
    }
  ];
  
  for (const channel of channels) {
    try {
      const [result] = await monitoring.createNotificationChannel({
        name: projectPath,
        notificationChannel: channel
      });
      console.log(`✅ Canal de notification créé: ${result.displayName}`);
    } catch (error) {
      if (error.code === 6) { // ALREADY_EXISTS
        console.log(`ℹ️ Canal déjà existant: ${channel.displayName}`);
      } else {
        console.error(`❌ Erreur création canal ${channel.displayName}:`, error);
      }
    }
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 CONFIGURATION CLOUD MONITORING SIGMA DASHBOARD');
  console.log('='.repeat(60));
  
  try {
    // 1. Créer les métriques personnalisées
    await createCustomMetrics();
    
    // 2. Configurer les canaux de notification
    await setupNotificationChannels();
    
    // 3. Créer les politiques d'alerte
    console.log('\n📊 Configuration des alertes...');
    const results = [];
    
    for (const policy of ALERT_POLICIES) {
      const result = await createOrUpdateAlertPolicy(policy);
      results.push(result);
    }
    
    // 4. Résumé
    console.log('\n✅ CONFIGURATION TERMINÉE');
    console.log(`📊 ${results.length} alertes configurées`);
    console.log(`📈 ${3} métriques personnalisées`);
    console.log(`📧 ${2} canaux de notification`);
    
    console.log('\n📋 Alertes configurées:');
    results.forEach((policy, index) => {
      console.log(`  ${index + 1}. ${policy.displayName}`);
    });
    
    console.log('\n💡 Prochaines étapes:');
    console.log('  1. Vérifier les alertes dans la Console Cloud Monitoring');
    console.log('  2. Tester les notifications avec des métriques de test');
    console.log('  3. Ajuster les seuils selon les besoins métier');
    console.log('  4. Configurer l\'URL Slack si nécessaire');
    
  } catch (error) {
    console.error('❌ Erreur lors de la configuration:', error);
    process.exit(1);
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  main();
}

module.exports = {
  createOrUpdateAlertPolicy,
  createCustomMetrics,
  setupNotificationChannels,
  ALERT_POLICIES
};
