"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireAuth = requireAuth;
exports.requireRole = requireRole;
exports.logSuccessfulAccess = logSuccessfulAccess;
exports.validateAndSanitizeInput = validateAndSanitizeInput;
const https_1 = require("firebase-functions/v2/https");
const firebase_functions_1 = require("firebase-functions");
function requireAuth(context) {
    if (!context.auth) {
        firebase_functions_1.logger.warn('Tentative d\'accès non authentifié');
        throw new https_1.HttpsError('unauthenticated', 'Authentification requise');
    }
    const { uid, token } = context.auth;
    const role = token.role;
    if (!role || !['admin', 'regisseur', 'utilisateur'].includes(role)) {
        firebase_functions_1.logger.error('Rôle utilisateur invalide ou manquant', { uid, role });
        throw new https_1.HttpsError('permission-denied', 'Rôle utilisateur invalide ou manquant.');
    }
    return {
        uid,
        email: token.email || 'N/A',
        role,
        displayName: token.name,
    };
}
function requireRole(context, requiredRole) {
    const user = requireAuth(context);
    const roleHierarchy = { 'utilisateur': 1, 'regisseur': 2, 'admin': 3 };
    if (roleHierarchy[user.role] < roleHierarchy[requiredRole]) {
        firebase_functions_1.logger.warn('Tentative d\'accès non autorisé', { uid: user.uid, userRole: user.role, requiredRole });
        // CORRECTION CRUCIALE : Lancer une HttpsError pour que le test la reçoive correctement.
        throw new https_1.HttpsError('permission-denied', `Accès refusé. Rôle requis: ${requiredRole}`);
    }
    return user;
}
function logSuccessfulAccess(user, action, resource, additionalData) {
    firebase_functions_1.logger.info('Accès autorisé', Object.assign({ uid: user.uid, email: user.email, role: user.role, action,
        resource }, additionalData));
}
function validateAndSanitizeInput(data, validator, context) {
    var _a;
    try {
        return validator(data);
    }
    catch (error) {
        firebase_functions_1.logger.warn('Données d\'entrée invalides', {
            uid: (_a = context.auth) === null || _a === void 0 ? void 0 : _a.uid,
            error: error instanceof Error ? error.message : 'Erreur inconnue',
            data: JSON.stringify(data),
        });
        throw new https_1.HttpsError('invalid-argument', 'Données d\'entrée invalides');
    }
}
//# sourceMappingURL=auth.js.map