"use strict";
/**
 * Utilitaires de validation pour les Cloud Functions SIGMA
 * Validation des données métier et des permissions spécifiques
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LivraisonSchema = exports.ModuleSchema = exports.StockSchema = exports.EmpruntSchema = void 0;
exports.validateEmpruntAccess = validateEmpruntAccess;
exports.validateStockAvailability = validateStockAvailability;
exports.validateModuleAvailability = validateModuleAvailability;
exports.validateEmpruntDates = validateEmpruntDates;
exports.validateEmpruntStatusTransition = validateEmpruntStatusTransition;
exports.logSimpleValidationError = logSimpleValidationError;
exports.sanitizeInput = sanitizeInput;
exports.validateEmpruntData = validateEmpruntData;
const zod_1 = require("zod");
const firestore_1 = require("firebase-admin/firestore");
const firebase_functions_1 = require("firebase-functions");
const functions = __importStar(require("firebase-functions"));
// Schémas de validation pour les entités métier
// Validation des emprunts
exports.EmpruntSchema = zod_1.z
    .object({
    nom: zod_1.z.string().min(1, "Nom de l'emprunt requis").max(100, "Nom trop long"),
    lieu: zod_1.z.string().min(1, "Lieu requis").max(100, "Lieu trop long"),
    dateDepart: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), "Date de départ invalide"),
    dateRetour: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), "Date de retour invalide"),
    emprunteur: zod_1.z.string().min(1, "Emprunteur requis").max(100, "Nom emprunteur trop long"),
    referent: zod_1.z.string().optional(),
    secteur: zod_1.z.string().optional(),
    notes: zod_1.z.string().optional(),
    statut: zod_1.z.enum(["Pas prêt", "Prêt", "Parti", "Revenu", "Inventorié"], {
        message: "Statut invalide",
    }),
    materiel: zod_1.z
        .array(zod_1.z.object({
        stockId: zod_1.z.string().min(1, "ID stock requis"),
        nom: zod_1.z.string().min(1, "Nom matériel requis"),
        quantite: zod_1.z.number().int().min(1, "Quantité doit être positive"),
        quantiteRetournee: zod_1.z.number().int().min(0, "Quantité retournée invalide").optional(),
    }))
        .optional(),
    estInventorie: zod_1.z.boolean().optional(),
    estFacture: zod_1.z.boolean().optional(),
})
    .refine((data) => new Date(data.dateRetour) > new Date(data.dateDepart), {
    message: "La date de retour doit être après la date de départ",
    path: ["dateRetour"],
});
// Validation des stocks
exports.StockSchema = zod_1.z.object({
    nom: zod_1.z.string().min(1, "Nom de l'article requis").max(100, "Nom trop long"),
    description: zod_1.z.string().optional(),
    categorie: zod_1.z.string().min(1, "Catégorie requise").max(50, "Catégorie trop longue"),
    quantite: zod_1.z.number().int().min(0, "Quantité doit être positive ou nulle"),
    seuil: zod_1.z.number().int().min(0, "Seuil doit être positif ou nul"),
    unite: zod_1.z.string().min(1, "Unité requise").max(20, "Unité trop longue"),
    prix: zod_1.z.number().min(0, "Prix doit être positif ou nul").optional(),
    fournisseur: zod_1.z.string().optional(),
    reference: zod_1.z.string().optional(),
    emplacement: zod_1.z.string().optional(),
    estActif: zod_1.z.boolean().optional(),
});
// Validation des modules
exports.ModuleSchema = zod_1.z.object({
    nom: zod_1.z.string().min(1, "Nom du module requis").max(100, "Nom trop long"),
    description: zod_1.z.string().optional(),
    categorie: zod_1.z.string().min(1, "Catégorie requise").max(50, "Catégorie trop longue"),
    contenu: zod_1.z
        .array(zod_1.z.object({
        stockId: zod_1.z.string().min(1, "ID stock requis"),
        nom: zod_1.z.string().min(1, "Nom matériel requis"),
        quantite: zod_1.z.number().int().min(1, "Quantité doit être positive"),
    }))
        .min(1, "Le module doit contenir au moins un élément"),
    estPret: zod_1.z.boolean().optional(),
    estActif: zod_1.z.boolean().optional(),
    notes: zod_1.z.string().optional(),
});
// Validation des livraisons
exports.LivraisonSchema = zod_1.z.object({
    empruntRef: zod_1.z.string().min(1, "Référence emprunt requise"),
    type: zod_1.z.enum(["aller", "retour"], {
        message: "Type de livraison invalide",
    }),
    datePrevu: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), "Date prévue invalide"),
    adresse: zod_1.z.object({
        rue: zod_1.z.string().min(1, "Rue requise"),
        ville: zod_1.z.string().min(1, "Ville requise"),
        codePostal: zod_1.z.string().min(5, "Code postal invalide").max(10, "Code postal trop long"),
        pays: zod_1.z.string().min(1, "Pays requis").optional(),
    }),
    contact: zod_1.z.object({
        nom: zod_1.z.string().min(1, "Nom contact requis"),
        telephone: zod_1.z.string().optional(),
        email: zod_1.z.string().email("Email invalide").optional(),
    }),
    statut: zod_1.z.enum(["planifiee", "en_cours", "livree", "annulee"], {
        message: "Statut de livraison invalide",
    }),
    transporteur: zod_1.z.string().optional(),
    numeroSuivi: zod_1.z.string().optional(),
    notes: zod_1.z.string().optional(),
});
/**
 * Vérifier si un emprunt existe et est accessible par l'utilisateur
 */
async function validateEmpruntAccess(empruntId, user, requireOwnership = false) {
    const db = (0, firestore_1.getFirestore)();
    const empruntDoc = await db.collection("emprunts").doc(empruntId).get();
    if (!empruntDoc.exists) {
        throw new Error("Emprunt introuvable");
    }
    const empruntData = empruntDoc.data();
    // Vérifier la propriété si requis
    if (requireOwnership && user.role !== "admin" && user.role !== "regisseur") {
        if (empruntData.createdBy !== user.uid) {
            throw new Error("Accès refusé. Vous n'êtes pas propriétaire de cet emprunt.");
        }
    }
    return empruntDoc;
}
/**
 * Vérifier si un stock existe et a suffisamment de quantité
 */
async function validateStockAvailability(stockId, quantiteRequise) {
    const db = (0, firestore_1.getFirestore)();
    const stockDoc = await db.collection("stocks").doc(stockId).get();
    if (!stockDoc.exists) {
        throw new Error(`Stock ${stockId} introuvable`);
    }
    const stockData = stockDoc.data();
    if (!stockData.estActif) {
        throw new Error(`Stock ${stockData.nom} inactif`);
    }
    if (stockData.quantite < quantiteRequise) {
        throw new Error(`Stock insuffisant pour ${stockData.nom}. Disponible: ${stockData.quantite}, Requis: ${quantiteRequise}`);
    }
    return stockDoc;
}
/**
 * Vérifier si un module existe et est disponible
 */
async function validateModuleAvailability(moduleId) {
    const db = (0, firestore_1.getFirestore)();
    const moduleDoc = await db.collection("modules").doc(moduleId).get();
    if (!moduleDoc.exists) {
        throw new Error(`Module ${moduleId} introuvable`);
    }
    const moduleData = moduleDoc.data();
    if (!moduleData.estActif) {
        throw new Error(`Module ${moduleData.nom} inactif`);
    }
    if (!moduleData.estPret) {
        throw new Error(`Module ${moduleData.nom} non prêt`);
    }
    return moduleDoc;
}
/**
 * Valider les dates d'emprunt
 */
function validateEmpruntDates(dateDepart, dateRetour) {
    const depart = new Date(dateDepart);
    const retour = new Date(dateRetour);
    const maintenant = new Date();
    if (depart < maintenant) {
        throw new Error("La date de départ ne peut pas être dans le passé");
    }
    if (retour <= depart) {
        throw new Error("La date de retour doit être après la date de départ");
    }
    const dureeMax = 365 * 24 * 60 * 60 * 1000; // 1 an en millisecondes
    if (retour.getTime() - depart.getTime() > dureeMax) {
        throw new Error("La durée d'emprunt ne peut pas dépasser 1 an");
    }
}
/**
 * Valider les transitions de statut d'emprunt
 */
function validateEmpruntStatusTransition(currentStatus, newStatus, userRole) {
    const validTransitions = {
        "Pas prêt": ["Prêt", "Pas prêt"], // Peut rester en "Pas prêt"
        Prêt: ["Parti", "Pas prêt"], // Peut revenir en "Pas prêt" si problème
        Parti: ["Revenu"],
        Revenu: ["Inventorié"],
        Inventorié: [], // État final
    };
    if (!validTransitions[currentStatus]?.includes(newStatus)) {
        throw new Error(`Transition de statut invalide: ${currentStatus} → ${newStatus}`);
    }
    // Seuls les régisseurs et admins peuvent faire certaines transitions
    const restrictedTransitions = ["Prêt", "Parti", "Revenu", "Inventorié"];
    if (restrictedTransitions.includes(newStatus) && !["admin", "regisseur"].includes(userRole)) {
        throw new Error(`Seuls les régisseurs peuvent définir le statut "${newStatus}"`);
    }
}
/**
 * Logger les validations échouées (version simple)
 */
function logSimpleValidationError(error, context) {
    firebase_functions_1.logger.warn("Erreur de validation", {
        userId: context.userId,
        action: context.action,
        error: error.message,
        data: context.data ? JSON.stringify(context.data) : undefined,
        timestamp: new Date().toISOString(),
    });
}
/**
 * Nettoyer et normaliser les données d'entrée
 */
function sanitizeInput(data) {
    if (typeof data === "string") {
        return data.trim();
    }
    if (Array.isArray(data)) {
        return data.map(sanitizeInput);
    }
    if (data && typeof data === "object") {
        const sanitized = {};
        for (const [key, value] of Object.entries(data)) {
            sanitized[key] = sanitizeInput(value);
        }
        return sanitized;
    }
    return data;
}
/**
 * Valide les données d'un emprunt en utilisant le schéma Zod
 */
function validateEmpruntData(data) {
    try {
        return exports.EmpruntSchema.parse(data);
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            const firstError = error.issues[0];
            throw new functions.https.HttpsError("invalid-argument", `Validation échouée: ${firstError.message}`);
        }
        throw new functions.https.HttpsError("invalid-argument", "Données invalides");
    }
}
// --- FIN DU FICHIER ---
