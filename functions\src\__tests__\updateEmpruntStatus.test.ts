import { updateEmpruntStatus } from "../emprunts/updateEmpruntStatus";
import * as admin from "firebase-admin";
import { adminDb } from "../tests/setupTests";

describe("updateEmpruntStatus Cloud Function", () => {
  let testEnv: any;
  let wrapped: any;
  let mockRunTransaction: jest.Mock;

  beforeAll(() => {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    testEnv = require("firebase-functions-test")();
    
    // Initialiser Firebase Admin est maintenant fait dans setupTests
    
    // Créer un mock pour runTransaction
    mockRunTransaction = jest.fn().mockImplementation((callback) => {
      const transaction = {
        get: jest.fn().mockResolvedValue({
          exists: true,
          data: () => ({
            statut: "Prêt"
          })
        }),
        set: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      };
      return callback(transaction);
    });
    
    // Espionner la méthode runTransaction
    jest.spyOn(adminDb, "runTransaction").mockImplementation(mockRunTransaction);
    
    wrapped = testEnv.wrap(updateEmpruntStatus);
  });

  afterAll(() => {
    testEnv.cleanup();
    jest.restoreAllMocks();
  });

  test("devrait mettre à jour un statut avec succès", async () => {
    const data = {
      empruntId: "test-emprunt-id",
      newStatus: "Parti"
    };

    const context = {
      auth: {
        uid: "test-uid",
        token: {
          email: "<EMAIL>",
          role: "admin"
        }
      }
    };

    const result = await wrapped(data, context);
    expect(result).toEqual({ success: true, newStatus: "Parti" });
    // Vérifier que notre mock a été appelé
    expect(mockRunTransaction).toHaveBeenCalled();
  });
});
