/**
 * SIGMA - Système Informatique de Gestion du Matériel
 * Code principal Google Apps Script
 *
 * Ce fichier gère le routage principal et l'authentification
 */

/**
 * Fonction principale appelée lors de l'ouverture de l'application
 */
function doGet(e) {
  try {
    // Récupérer le paramètre de page
    const page = e.parameter.page || 'dashboard';

    // Log de la requête
    console.log(`📄 Requête reçue pour la page: ${page}`);

    // Routage vers la page appropriée
    switch (page) {
      case 'login':
        return serveLoginPage();
      case 'dashboard':
        return serveDashboard();
      case 'emprunts':
        return serveEmprunts();
      case 'stocks':
        return serveStocks();
      case 'modules':
        return serveModules();
      case 'livraisons':
        return serveLivraisons();
      case 'admin':
        return serveAdmin();
      default:
        return serveDashboard(); // Page par défaut
    }
  } catch (error) {
    console.error('❌ Erreur dans doGet:', error);
    return serveErrorPage(error.message);
  }
}

/**
 * Servir la page de connexion
 */
function serveLoginPage() {
  try {
    const template = HtmlService.createTemplateFromFile('html/login');
    const html = template.evaluate()
      .setTitle('SIGMA - Connexion')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1.0');

    return html;
  } catch (error) {
    console.error('❌ Erreur lors du chargement de la page de connexion:', error);
    throw error;
  }
}

/**
 * Servir le dashboard principal
 */
function serveDashboard() {
  try {
    // Pour l'instant, rediriger vers la page de connexion
    // TODO: Implémenter la vérification d'authentification
    return serveLoginPage();
  } catch (error) {
    console.error('❌ Erreur lors du chargement du dashboard:', error);
    throw error;
  }
}

/**
 * Servir la page des emprunts
 */
function serveEmprunts() {
  try {
    // TODO: Implémenter la page des emprunts
    return serveLoginPage();
  } catch (error) {
    console.error('❌ Erreur lors du chargement des emprunts:', error);
    throw error;
  }
}

/**
 * Servir la page des stocks
 */
function serveStocks() {
  try {
    // TODO: Implémenter la page des stocks
    return serveLoginPage();
  } catch (error) {
    console.error('❌ Erreur lors du chargement des stocks:', error);
    throw error;
  }
}

/**
 * Servir la page des modules
 */
function serveModules() {
  try {
    // TODO: Implémenter la page des modules
    return serveLoginPage();
  } catch (error) {
    console.error('❌ Erreur lors du chargement des modules:', error);
    throw error;
  }
}

/**
 * Servir la page des livraisons
 */
function serveLivraisons() {
  try {
    // TODO: Implémenter la page des livraisons
    return serveLoginPage();
  } catch (error) {
    console.error('❌ Erreur lors du chargement des livraisons:', error);
    throw error;
  }
}

/**
 * Servir la page d'administration
 */
function serveAdmin() {
  try {
    // TODO: Implémenter la page d'administration
    return serveLoginPage();
  } catch (error) {
    console.error('❌ Erreur lors du chargement de l\'administration:', error);
    throw error;
  }
}

/**
 * Servir une page d'erreur
 */
function serveErrorPage(errorMessage) {
  try {
    const html = HtmlService.createHtmlOutput(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>SIGMA - Erreur</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 50px;
              background: #f5f5f5;
            }
            .error-container {
              background: white;
              padding: 30px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              max-width: 500px;
              margin: 0 auto;
            }
            h1 { color: #d32f2f; }
            p { color: #666; margin: 20px 0; }
            .retry-btn {
              background: #1976d2;
              color: white;
              padding: 10px 20px;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              text-decoration: none;
              display: inline-block;
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <h1>⚠️ Erreur</h1>
            <p>Une erreur est survenue lors du chargement de la page.</p>
            <p><strong>Détails:</strong> ${errorMessage}</p>
            <a href="?" class="retry-btn">Réessayer</a>
          </div>
        </body>
      </html>
    `)
      .setTitle('SIGMA - Erreur')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    return html;
  } catch (error) {
    console.error('❌ Erreur lors de la création de la page d\'erreur:', error);
    // Fallback simple
    return HtmlService.createHtmlOutput('<h1>Erreur système</h1><p>Veuillez contacter l\'administrateur.</p>');
  }
}

/**
 * Fonction utilitaire pour inclure des fichiers CSS/JS
 */
function include(filename) {
  try {
    return HtmlService.createHtmlOutputFromFile(filename).getContent();
  } catch (error) {
    console.error(`❌ Erreur lors de l'inclusion du fichier ${filename}:`, error);
    return `<!-- Erreur: impossible de charger ${filename} -->`;
  }
}