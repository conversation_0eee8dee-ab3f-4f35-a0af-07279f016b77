{"version": 3, "file": "setUserRole.js", "sourceRoot": "", "sources": ["../../src/auth/setUserRole.ts"], "names": [], "mappings": ";;;AAAA,oCAAoC;AACpC,uDAAiE;AACjE,2DAA4C;AAC5C,8CAA8C;AAC9C,wDAAwD;AAExD,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;AACrE,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC;IAChC,WAAW,EAAC,SAAS,EAAC,kBAAkB,EAAC,mBAAmB,EAAC,WAAW,EAAC,gBAAgB;IACzF,mBAAmB,EAAC,oBAAoB,EAAC,qBAAqB,EAAC,SAAS,EAAC,cAAc;IACvF,eAAe,EAAC,UAAU,EAAC,aAAa,EAAC,WAAW,EAAC,iBAAiB;CACvE,CAAC,CAAC;AAEU,QAAA,WAAW,GAAG,IAAA,cAAM,EAAC,EAAE,MAAM,EAAE,cAAc,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;;IAC9E,IAAI,CAAC;QACH,kBAAkB;QAClB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,2BAA2B,CAAC,CAAC;QACvE,CAAC;QAED,8CAA8C;QAC9C,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QACtC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAA,MAAA,OAAO,CAAC,IAAI,CAAC,KAAK,0CAAE,IAAI,mCAAI,aAAa,CAAC,CAAC;QACxE,IAAI,aAAa,KAAK,OAAO,EAAE,CAAC;YAC9B,2BAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,GAAG,EAAE,YAAY;gBACjB,QAAQ,EAAE,aAAa;gBACvB,YAAY,EAAE,OAAO;aACtB,CAAC,CAAC;YACH,MAAM,IAAI,kBAAU,CAAC,mBAAmB,EAAE,kCAAkC,CAAC,CAAC;QAChF,CAAC;QAED,sBAAsB;QACtB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAA,MAAA,MAAA,OAAO,CAAC,IAAI,0CAAE,MAAM,mCAAI,MAAA,OAAO,CAAC,IAAI,0CAAE,GAAG,mCAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9E,MAAM,IAAI,GAAG,MAAM,CAAC,MAAA,MAAA,OAAO,CAAC,IAAI,0CAAE,IAAI,mCAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QACrD,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,kBAAU,CAAC,kBAAkB,EAAE,4BAA4B,CAAC,CAAC;QACpF,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,kBAAU,CAAC,kBAAkB,EAAE,4BAA4B,CAAC,CAAC;QACzE,CAAC;QAED,iCAAiC;QACjC,MAAM,IAAI,GAAG,IAAA,cAAO,GAAE,CAAC;QACvB,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB;QAEhD,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QAEjD,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;QAC1B,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAExE,gBAAgB;QAChB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC1C,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,2BAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;YACtC,IAAI,EAAE,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,mCAAI,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,SAAS,0CAAE,IAAI;YACvC,OAAO,EAAE,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,OAAO;YACrB,KAAK,EAAE,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK;SAClB,CAAC,CAAC;QAEH,6EAA6E;QAC7E,IAAI,OAAO,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,CAAA,KAAK,QAAQ,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACrE,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,qCAAqC;QACrC,MAAM,SAAS,GAAG,CAAA,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,SAAS,0CAAE,IAAI,MAAI,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,CAAA,CAAC;QACpD,IAAI,SAAS,KAAK,qBAAqB,EAAE,CAAC;YACxC,MAAM,IAAI,kBAAU,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAC;QAChE,CAAC;QAED,qBAAqB;QACrB,MAAM,IAAI,kBAAU,CAAC,UAAU,EAAE,2BAA2B,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CAAC,CAAC"}