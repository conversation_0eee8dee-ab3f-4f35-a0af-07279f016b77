# Spécifications des Requêtes Dashboard SIGMA

*Version : 1.0 - Requêtes optimisées*  
*Auteur : Agent IA - Mission E-3*

## 1. Mapping des 7 tableaux dashboard

### 1.1 Alertes Stock 🚨
**Objectif** : Articles avec stock critique (quantité ≤ seuil)
```typescript
// Requête optimisée avec index composite
db.collection('stocks')
  .where('quantite', '<=', 'seuilAlerte')  // Comparaison dynamique
  .where('estOperationnel', '==', true)
  .orderBy('quantite', 'asc')
  .limit(20)
```
**Index requis** : `(quantite, seuilAlerte, estOperationnel)`

### 1.2 Matériel spécifique manquant ❌
**Objectif** : Articles marqués à commander
```typescript
db.collection('stocks')
  .where('aCommander', '==', true)
  .orderBy('updatedAt', 'desc')
  .limit(20)
```
**Index requis** : `(a<PERSON><PERSON><PERSON><PERSON>, updatedAt)`

### 1.3 Emprunts non revenus 🚨
**Objectif** : Emprunts "Parti" avec date retour dépassée
```typescript
const now = new Date();
db.collection('emprunts')
  .where('statut', '==', 'Parti')
  .where('dateRetourPrevue', '<', now)
  .orderBy('dateRetourPrevue', 'asc')
  .limit(20)
```
**Index requis** : `(statut, dateRetourPrevue)` ✅ *Existant*

### 1.4 Prochains emprunts 📅
**Objectif** : Départs dans les 30 prochains jours
```typescript
const now = new Date();
const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

db.collection('emprunts')
  .where('statut', 'in', ['Pas prêt', 'Prêt'])
  .where('dateDepart', '>=', now)
  .where('dateDepart', '<=', in30Days)
  .orderBy('dateDepart', 'asc')
  .limit(20)
```
**Index requis** : `(statut, dateDepart)` ✅ *Nouveau*

### 1.5 Modules non opérationnels 📌
**Objectif** : Modules non prêts (malles à réassortir)
```typescript
db.collection('modules')
  .where('estPret', '==', false)
  .orderBy('updatedAt', 'desc')
  .limit(20)
```
**Index requis** : `(estPret, updatedAt)` ✅ *Nouveau*

### 1.6 Matériel non opérationnel 🔧
**Objectif** : Matériel dégradé ou en réparation
```typescript
db.collection('stocks')
  .where('estOperationnel', '==', false)
  .orderBy('updatedAt', 'desc')
  .limit(20)
```
**Index requis** : `(estOperationnel, updatedAt)` ✅ *Nouveau*

### 1.7 Emprunts en attente 📋
**Objectif** : Non inventoriés ET/OU non facturés
```typescript
// Option A: Non inventoriés OU non facturés
db.collection('emprunts')
  .where('statut', '==', 'Revenu')
  .where('estInventorie', '==', false)
  .orderBy('dateRetourEffective', 'desc')
  .limit(10)

// Option B: Non facturés
db.collection('emprunts')
  .where('statut', '==', 'Revenu') 
  .where('estFacture', '==', false)
  .orderBy('dateRetourEffective', 'desc')
  .limit(10)
```
**Index requis** : `(estInventorie, estFacture, statut)` ✅ *Nouveau*

## 2. Performance & Optimisations

### 2.1 Contraintes respectées
- ✅ **Limite 20 documents** par requête (< 100 lectures/sec)
- ✅ **Index composites** pour toutes les requêtes filtrées
- ✅ **Pagination** avec `startAfter` pour navigation
- ✅ **Cache offline** Firestore pour PWA

### 2.2 Stratégie de mise à jour temps-réel
```typescript
// Pattern listener avec debouncing
class DashboardListener {
  private debounceTimers = new Map<string, NodeJS.Timeout>();
  
  setupListener(tableId: string, query: Query) {
    return query.onSnapshot(
      (snapshot) => this.debounceUpdate(tableId, snapshot),
      (error) => this.handleError(tableId, error)
    );
  }
  
  private debounceUpdate(tableId: string, snapshot: QuerySnapshot) {
    clearTimeout(this.debounceTimers.get(tableId));
    this.debounceTimers.set(tableId, setTimeout(() => {
      this.updateTable(tableId, snapshot.docs);
    }, 200)); // 200ms debounce
  }
}
```

### 2.3 Gestion des erreurs et fallback
```typescript
// Stratégie de récupération progressive
async function getDashboardDataWithFallback() {
  const results = await Promise.allSettled([
    getStockAlerts(),
    getMissingMaterial(),
    getOverdueEmprunts(),
    getUpcomingEmprunts(),
    getNonOperationalModules(),
    getNonOperationalMaterial(),
    getPendingEmprunts()
  ]);
  
  return results.map((result, index) => ({
    tableId: TABLE_IDS[index],
    status: result.status,
    data: result.status === 'fulfilled' ? result.value : [],
    error: result.status === 'rejected' ? result.reason : null
  }));
}
```

## 3. Monitoring & Alertes

### 3.1 Métriques clés à surveiller
- **Latence requêtes** : < 500ms par tableau
- **Lectures Firestore** : < 100/sec en charge nominale
- **Erreurs listeners** : < 1% taux d'échec
- **Cache hit ratio** : > 80% pour données fréquentes

### 3.2 Alertes Cloud Monitoring
```typescript
// Configuration alertes automatiques
const alertPolicies = [
  {
    name: 'Dashboard Latency High',
    condition: 'dashboard_query_duration > 500ms',
    threshold: '95th percentile over 2 minutes'
  },
  {
    name: 'Firestore Read Quota',
    condition: 'firestore_reads > 100/sec',
    threshold: 'sustained for 1 minute'
  },
  {
    name: 'Dashboard Error Rate',
    condition: 'dashboard_errors > 5%',
    threshold: 'over 5 minutes'
  }
];
```

## 4. Tests de validation

### 4.1 Tests de performance
```typescript
describe('Dashboard Queries Performance', () => {
  test('Stock alerts query < 200ms', async () => {
    const start = Date.now();
    await getStockAlerts();
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(200);
  });
  
  test('All queries combined < 1000ms', async () => {
    const start = Date.now();
    await getDashboardData();
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(1000);
  });
});
```

### 4.2 Tests de charge
```typescript
// Simulation 50 utilisateurs simultanés
test('Dashboard handles 50 concurrent users', async () => {
  const promises = Array(50).fill(null).map(() => getDashboardData());
  const results = await Promise.allSettled(promises);
  
  const failures = results.filter(r => r.status === 'rejected');
  expect(failures.length).toBeLessThan(5); // < 10% échec acceptable
});
```

## 5. Migration et déploiement

### 5.1 Étapes de déploiement
1. **Déployer index Firestore** : `firebase deploy --only firestore:indexes`
2. **Attendre création index** : Vérifier Firebase Console (peut prendre 10-30min)
3. **Déployer Cloud Functions** : `firebase deploy --only functions`
4. **Déployer frontend** : `clasp push` pour Google Apps Script
5. **Tests E2E** : Validation complète en environnement

### 5.2 Rollback strategy
```typescript
// Version de fallback sans index optimisés
const FALLBACK_QUERIES = {
  stockAlerts: () => db.collection('stocks').limit(20),
  overdueEmprunts: () => db.collection('emprunts').limit(20)
  // ... requêtes simplifiées
};
```

---

*Spécifications validées pour performance < 100 lectures/sec*
