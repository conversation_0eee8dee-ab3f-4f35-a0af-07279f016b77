import { createEmprunt } from "../emprunts/createEmprunt";
import * as admin from "firebase-admin";
import { adminDb } from "../tests/setupTests";

describe("createEmprunt Cloud Function", () => {
  let testEnv: any;
  let wrapped: any;
  let mockRunTransaction: jest.Mock;

  beforeAll(() => {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    testEnv = require("firebase-functions-test")();
    
    // Initialiser Firebase Admin est maintenant fait dans setupTests
    
    // Créer un mock pour runTransaction
    mockRunTransaction = jest.fn().mockImplementation((callback) => {
      const transaction = {
        get: jest.fn().mockResolvedValue({
          exists: false
        }),
        set: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      };
      return callback(transaction);
    });
    
    // Espionner la méthode runTransaction
    jest.spyOn(adminDb, "runTransaction").mockImplementation(mockRunTransaction);
    
    wrapped = testEnv.wrap(createEmprunt);
  });

  afterAll(() => {
    testEnv.cleanup();
    jest.restoreAllMocks();
  });

  test("devrait créer un emprunt avec succès", async () => {
    const data = {
      nom: "Test Emprunt",
      lieu: "Studio A",
      dateDepart: "2023-01-01",
      dateRetourPrevue: "2023-01-02",
      referent: "John Doe",
      emprunteur: "Jane Doe",
      materiel: []
    };

    const context = {
      auth: {
        uid: "test-uid",
        token: {
          email: "<EMAIL>",
          role: "admin"
        }
      }
    };

    const result = await wrapped(data, context);
    expect(result).toHaveProperty("id");
    // Vérifier que notre mock a été appelé
    expect(mockRunTransaction).toHaveBeenCalled();
  });
});
