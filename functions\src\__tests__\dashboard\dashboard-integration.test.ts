/**
 * Tests d'intégration pour les fonctions dashboard
 * Tests simplifiés utilisant firebase-functions-test
 */

import * as admin from 'firebase-admin';
import functionsTest from 'firebase-functions-test';

// Mock des utilitaires de monitoring
jest.mock('../../utils/monitoring', () => ({
  sendCriticalStockCount: jest.fn(),
  sendCriticalOverdueCount: jest.fn(),
  sendFirestoreReadRate: jest.fn(),
  withMonitoring: (name: string, fn: Function) => fn
}));

// Initialiser firebase-functions-test
const testEnv = functionsTest({
  projectId: 'sigma-nova'
});

// Importer les fonctions après l'initialisation
import { getDashboardData } from '../../dashboard/getDashboardData';
import { getStockAlerts } from '../../dashboard/getStockAlerts';
import { getOverdueEmprunts } from '../../dashboard/getOverdueEmprunts';

describe('Dashboard Functions Integration', () => {
  let db: admin.firestore.Firestore;
  let auth: admin.auth.Auth;

  beforeAll(async () => {
    // Initialiser Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        projectId: 'sigma-nova'
      });
    }
    
    db = admin.firestore();
    auth = admin.auth();
  });

  beforeEach(async () => {
    // Nettoyer les données de test
    await clearTestData();
    await createTestData();
  });

  afterAll(async () => {
    // Nettoyer et fermer
    await clearTestData();
    testEnv.cleanup();
  });

  describe('getDashboardData', () => {
    test('devrait retourner une structure de données valide', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      // Wrapper la fonction pour les tests
      const wrappedFunction = test.wrap(getDashboardData);
      const result = await wrappedFunction(data, context);

      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
      
      // Vérifier la structure de base
      if (result && typeof result === 'object') {
        expect(result).toHaveProperty('timestamp');
        expect(result).toHaveProperty('performance');
      }
    });

    test('devrait rejeter les utilisateurs non autorisés', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'utilisateur' }
        }
      };

      const wrappedFunction = test.wrap(getDashboardData);
      
      await expect(wrappedFunction(data, context))
        .rejects
        .toThrow();
    });
  });

  describe('getStockAlerts', () => {
    test('devrait retourner des alertes stock', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const wrappedFunction = test.wrap(getStockAlerts);
      const result = await wrappedFunction(data, context);

      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
      
      if (result && typeof result === 'object') {
        expect(result).toHaveProperty('timestamp');
        expect(result).toHaveProperty('performance');
      }
    });

    test('devrait filtrer par sévérité', async () => {
      const data = { severity: 'critical' };
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const wrappedFunction = test.wrap(getStockAlerts);
      const result = await wrappedFunction(data, context);

      expect(result).toBeDefined();
    });
  });

  // Note: Tests déplacés vers getOverdueEmprunts.test.ts
  // Voir ce fichier pour les tests unitaires détaillés de getOverdueEmprunts

  describe('Performance', () => {
    test('les fonctions devraient s\'exécuter rapidement', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const startTime = Date.now();
      
      const wrappedFunction = test.wrap(getDashboardData);
      await wrappedFunction(data, context);
      
      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(2000); // 2 secondes max
    });
  });

  // Fonctions utilitaires
  async function clearTestData() {
    try {
      const collections = ['stocks', 'emprunts', 'modules'];
      
      for (const collectionName of collections) {
        const snapshot = await db.collection(collectionName).get();
        const batch = db.batch();
        
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        
        if (snapshot.docs.length > 0) {
          await batch.commit();
        }
      }
    } catch (error) {
      // Ignorer les erreurs de nettoyage
      console.warn('Erreur lors du nettoyage:', error);
    }
  }

  async function createTestData() {
    try {
      const batch = db.batch();
      const now = new Date();

      // Créer des stocks de test
      const stocksData = [
        {
          nom: 'Stock Critique Test',
          quantite: 2,
          seuilAlerte: 10,
          estOperationnel: true,
          localisation: 'Test Location',
          categorie: 'Test Category'
        },
        {
          nom: 'Stock Normal Test',
          quantite: 50,
          seuilAlerte: 10,
          estOperationnel: true,
          localisation: 'Test Location',
          categorie: 'Test Category'
        }
      ];

      stocksData.forEach((stock, index) => {
        const ref = db.collection('stocks').doc(`test-stock-${index}`);
        batch.set(ref, {
          ...stock,
          createdAt: now,
          updatedAt: now
        });
      });

      // Créer des emprunts de test
      const empruntsData = [
        {
          nom: 'Emprunt En Retard Test',
          statut: 'Parti',
          dateRetourPrevue: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 jours de retard
          dateDepart: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000),
          emprunteur: 'Test User',
          lieu: 'Test Location'
        },
        {
          nom: 'Prochain Emprunt Test',
          statut: 'Prêt',
          dateRetourPrevue: new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000),
          dateDepart: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000),
          emprunteur: 'Test User',
          lieu: 'Test Location'
        }
      ];

      empruntsData.forEach((emprunt, index) => {
        const ref = db.collection('emprunts').doc(`test-emprunt-${index}`);
        batch.set(ref, {
          ...emprunt,
          createdAt: now,
          updatedAt: now
        });
      });

      // Créer des modules de test
      const modulesData = [
        {
          nom: 'Module Test',
          estPret: false,
          type: 'Malle',
          localisation: 'Test Location'
        }
      ];

      modulesData.forEach((module, index) => {
        const ref = db.collection('modules').doc(`test-module-${index}`);
        batch.set(ref, {
          ...module,
          createdAt: now,
          updatedAt: now
        });
      });

      await batch.commit();
    } catch (error) {
      console.warn('Erreur lors de la création des données de test:', error);
    }
  }
});
