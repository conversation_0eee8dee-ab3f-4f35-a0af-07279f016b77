rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // --- Fonctions Utilitaires ---
    function isAuthenticated() {
      return request.auth != null;
    }

    function hasRole(role) {
      return isAuthenticated() && (request.auth.token.role == role || request.auth.token.role == 'admin');
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isAdmin() {
      return isAuthenticated() && request.auth.token.role == 'admin';
    }

    function isRegisseur() {
      return hasRole('regisseur');
    }

    function isUtilisateur() {
      return isAuthenticated() && request.auth.token.role in ['utilisateur', 'regisseur', 'admin'];
    }

    // --- Fonctions de Validation de Données ---
    function isValidEmpruntData(data) {
      return data.nom is string && data.nom.size() > 0 &&
             data.lieu is string && data.lieu.size() > 0 &&
             data.dateDepart is timestamp &&
             data.dateRetour is timestamp &&
             data.dateRetour > data.dateDepart &&
             data.statut in ['Pas prêt', 'Prêt', 'Parti', 'Revenu', 'Inventorié'] &&
             data.emprunteur is string && data.emprunteur.size() > 0;
    }

    function isValidStockData(data) {
      return data.nom is string && data.nom.size() > 0 &&
             data.quantite is number && data.quantite >= 0 &&
             data.seuil is number && data.seuil >= 0;
    }

    function isValidModuleData(data) {
      return data.nom is string && data.nom.size() > 0 &&
             data.description is string &&
             data.contenu is list;
    }

    function isValidLivraisonData(data) {
      return data.empruntRef is string && data.empruntRef.size() > 0 &&
             data.type in ['aller', 'retour'] &&
             data.statut in ['planifiee', 'en_cours', 'livree', 'annulee'] &&
             data.datePrevu is timestamp;
    }

    function isValidUserProfileUpdate(data) {
      return data.diff(resource.data).affectedKeys().hasOnly(['displayName', 'phoneNumber', 'preferences', 'stats']);
    }

    // --- RÈGLES PAR COLLECTION ---

    // Collection users : Profils utilisateurs
    match /users/{userId} {
      // Lecture : propriétaire ou admin
      allow get: if isOwner(userId) || isAdmin();

      // Liste : admin uniquement (via Cloud Function)
      allow list: if isAdmin();

      // Création : via Cloud Function uniquement (trigger onUserCreate)
      allow create: if false;

      // Suppression : via Cloud Function uniquement
      allow delete: if false;

      // Mise à jour : admin complet, utilisateur limité aux champs autorisés
      allow update: if isAdmin() ||
                      (isOwner(userId) && isValidUserProfileUpdate(request.resource.data));
    }

    // Collection emprunts : Gestion des prêts de matériel
    match /emprunts/{empruntId} {
      // Lecture : tous les utilisateurs authentifiés
      allow read: if isUtilisateur();

      // Création : tous les utilisateurs authentifiés avec validation
      allow create: if isUtilisateur() &&
                      isValidEmpruntData(request.resource.data) &&
                      request.resource.data.statut == 'Pas prêt' &&
                      request.resource.data.createdBy == request.auth.uid;

      // Mise à jour : admin et régisseur uniquement
      allow update: if isRegisseur() &&
                      request.resource.data.diff(resource.data).affectedKeys()
                        .hasOnly(['nom', 'lieu', 'dateDepart', 'dateRetour', 'secteur', 'emprunteur', 'referent', 'notes', 'statut', 'estInventorie', 'estFacture', 'materiel', 'updatedAt', 'updatedBy']);

      // Suppression : admin et régisseur uniquement
      allow delete: if isRegisseur();
    }

    // Collection stocks : Gestion des articles et quantités
    match /stocks/{stockId} {
      // Lecture : admin et régisseur uniquement
      allow read: if isRegisseur();

      // Création : admin et régisseur avec validation
      allow create: if isRegisseur() && isValidStockData(request.resource.data);

      // Mise à jour : admin et régisseur avec validation quantité >= 0
      allow update: if isRegisseur() &&
                      request.resource.data.quantite >= 0 &&
                      request.resource.data.seuil >= 0;

      // Suppression : admin et régisseur (attention aux références)
      allow delete: if isRegisseur();
    }

    // Collection modules : Ensembles de matériel préconfigurés
    match /modules/{moduleId} {
      // Lecture : tous les utilisateurs authentifiés
      allow read: if isUtilisateur();

      // Création : admin et régisseur avec validation
      allow create: if isRegisseur() && isValidModuleData(request.resource.data);

      // Mise à jour : admin et régisseur uniquement
      allow update: if isRegisseur();

      // Suppression : admin et régisseur (démantèlement)
      allow delete: if isRegisseur();
    }

    // Collection livraisons : Suivi des transports
    match /livraisons/{livraisonId} {
      // Lecture : tous les utilisateurs authentifiés
      allow read: if isUtilisateur();

      // Création : tous les utilisateurs avec validation emprunt existant
      allow create: if isUtilisateur() &&
                      isValidLivraisonData(request.resource.data) &&
                      request.resource.data.createdBy == request.auth.uid &&
                      exists(/databases/$(database)/documents/emprunts/$(request.resource.data.empruntRef));

      // Mise à jour : admin et régisseur uniquement (statut, preuve)
      allow update: if isRegisseur();

      // Suppression : admin et régisseur uniquement
      allow delete: if isRegisseur();
    }

    // Collection roleHistory : Historique des changements de rôles (admin uniquement)
    match /roleHistory/{historyId} {
      allow read: if isAdmin();
      allow write: if false; // Géré par Cloud Functions uniquement
    }

    // Collection auditLogs : Logs d'audit (admin uniquement)
    match /auditLogs/{logId} {
      allow read: if isAdmin();
      allow write: if false; // Géré par Cloud Functions uniquement
    }

    // Règle de sécurité par défaut : tout refuser
    match /{path=**} {
      allow read, write: if false;
    }
  }
}