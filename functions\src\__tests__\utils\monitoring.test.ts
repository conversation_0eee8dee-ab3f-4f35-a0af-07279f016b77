/**
 * Tests unitaires pour les utilitaires de monitoring
 * Validation de l'envoi de métriques et du middleware
 */

import {
  sendCustomMetric,
  sendCriticalStockCount,
  sendCriticalOverdueCount,
  sendDashboardQueryDuration,
  withMonitoring,
  MetricsBatch,
  METRIC_TYPES
} from '../../utils/monitoring';

// Mock du logger Firebase
jest.mock('firebase-functions', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

// Mock du client Cloud Monitoring
const mockCreateTimeSeries = jest.fn();
const mockProjectPath = jest.fn().mockReturnValue('projects/test-project');

jest.mock('@google-cloud/monitoring', () => ({
  MetricServiceClient: jest.fn().mockImplementation(() => ({
    createTimeSeries: mockCreateTimeSeries,
    projectPath: mockProjectPath
  }))
}));

describe('Monitoring Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Réinitialiser les variables d'environnement
    process.env.NODE_ENV = 'test';
    process.env.GCLOUD_PROJECT = 'test-project';
    process.env.FUNCTION_NAME = 'testFunction';
    process.env.FUNCTION_REGION = 'europe-west1';
  });

  describe('sendCustomMetric', () => {
    test('devrait bypasser en mode test', async () => {
      process.env.NODE_ENV = 'test';

      await sendCustomMetric({
        metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: 100
      });

      expect(mockCreateTimeSeries).not.toHaveBeenCalled();
    });

    test('devrait envoyer une métrique en mode production', async () => {
      process.env.NODE_ENV = 'production';
      mockCreateTimeSeries.mockResolvedValue([{}]);

      await sendCustomMetric({
        metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: 150,
        labels: { function_name: 'testFunction' }
      });

      expect(mockCreateTimeSeries).toHaveBeenCalledWith({
        name: 'projects/test-project',
        timeSeries: [{
          metric: {
            type: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
            labels: { function_name: 'testFunction' }
          },
          resource: {
            type: 'cloud_function',
            labels: {
              function_name: 'testFunction',
              region: 'europe-west1'
            }
          },
          points: [{
            interval: {
              endTime: {
                seconds: expect.any(Number)
              }
            },
            value: {
              doubleValue: 150
            }
          }]
        }]
      });
    });

    test('devrait gérer les erreurs d\'envoi', async () => {
      process.env.NODE_ENV = 'production';
      mockCreateTimeSeries.mockRejectedValue(new Error('Network error'));

      // Ne devrait pas lever d'exception
      await expect(sendCustomMetric({
        metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: 100
      })).resolves.toBeUndefined();
    });

    test('devrait utiliser un timestamp personnalisé', async () => {
      process.env.NODE_ENV = 'production';
      mockCreateTimeSeries.mockResolvedValue([{}]);

      const customTimestamp = new Date('2024-01-01T12:00:00Z');

      await sendCustomMetric({
        metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: 100,
        timestamp: customTimestamp
      });

      expect(mockCreateTimeSeries).toHaveBeenCalledWith(
        expect.objectContaining({
          timeSeries: [{
            metric: expect.any(Object),
            resource: expect.any(Object),
            points: [{
              interval: {
                endTime: {
                  seconds: Math.floor(customTimestamp.getTime() / 1000)
                }
              },
              value: {
                doubleValue: 100
              }
            }]
          }]
        })
      );
    });
  });

  describe('Fonctions de métriques spécialisées', () => {
    test('sendCriticalStockCount devrait être appelée sans erreur', async () => {
      // En mode test, la fonction devrait s'exécuter sans erreur
      await expect(sendCriticalStockCount(5)).resolves.toBeUndefined();
    });

    test('sendCriticalOverdueCount devrait être appelée sans erreur', async () => {
      // En mode test, la fonction devrait s'exécuter sans erreur
      await expect(sendCriticalOverdueCount(3)).resolves.toBeUndefined();
    });

    test('sendDashboardQueryDuration devrait être appelée sans erreur', async () => {
      // En mode test, la fonction devrait s'exécuter sans erreur
      await expect(sendDashboardQueryDuration('getDashboardData', 250)).resolves.toBeUndefined();
    });
  });

  describe('withMonitoring middleware', () => {
    test('devrait exécuter la fonction wrappée avec succès', async () => {
      const mockFunction = jest.fn().mockResolvedValue('success');

      const wrappedFunction = withMonitoring('testFunction', mockFunction);
      const result = await wrappedFunction('arg1', 'arg2');

      expect(result).toBe('success');
      expect(mockFunction).toHaveBeenCalledWith('arg1', 'arg2');
    });

    test('devrait propager les erreurs de la fonction wrappée', async () => {
      const mockFunction = jest.fn().mockRejectedValue(new Error('Test error'));

      const wrappedFunction = withMonitoring('testFunction', mockFunction);

      await expect(wrappedFunction('arg1')).rejects.toThrow('Test error');
      expect(mockFunction).toHaveBeenCalledWith('arg1');
    });
  });

  describe('MetricsBatch', () => {
    let metricsBatch: MetricsBatch;

    beforeEach(() => {
      metricsBatch = new MetricsBatch(3, 1000); // Batch de 3, flush toutes les 1 seconde
    });

    afterEach(() => {
      metricsBatch.stop();
    });

    test('devrait créer un batch sans erreur', () => {
      expect(metricsBatch).toBeDefined();
      expect(typeof metricsBatch.add).toBe('function');
      expect(typeof metricsBatch.flush).toBe('function');
      expect(typeof metricsBatch.stop).toBe('function');
    });

    test('devrait ajouter des métriques au batch', () => {
      expect(() => {
        metricsBatch.add({
          metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
          value: 100
        });
      }).not.toThrow();
    });

    test('devrait pouvoir flush manuellement', async () => {
      metricsBatch.add({
        metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: 100
      });

      await expect(metricsBatch.flush()).resolves.toBeUndefined();
    });

    test('ne devrait pas lever d\'erreur sur un batch vide', async () => {
      await expect(metricsBatch.flush()).resolves.toBeUndefined();
    });
  });

  describe('Gestion des erreurs et fallbacks', () => {
    test('devrait gérer l\'absence du client Cloud Monitoring', async () => {
      // Mock l'échec de l'import du client
      jest.doMock('@google-cloud/monitoring', () => {
        throw new Error('Module not found');
      });

      process.env.NODE_ENV = 'production';

      // Ne devrait pas lever d'exception
      await expect(sendCustomMetric({
        metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: 100
      })).resolves.toBeUndefined();
    });

    test('devrait utiliser les valeurs par défaut pour les variables d\'environnement manquantes', async () => {
      delete process.env.GCLOUD_PROJECT;
      delete process.env.FUNCTION_NAME;
      delete process.env.FUNCTION_REGION;

      process.env.NODE_ENV = 'production';
      mockCreateTimeSeries.mockResolvedValue([{}]);

      await sendCustomMetric({
        metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: 100
      });

      expect(mockCreateTimeSeries).toHaveBeenCalledWith(
        expect.objectContaining({
          timeSeries: [{
            metric: expect.any(Object),
            resource: {
              type: 'cloud_function',
              labels: {
                function_name: 'unknown',
                region: 'europe-west1'
              }
            },
            points: expect.any(Array)
          }]
        })
      );
    });
  });

  describe('Types de métriques', () => {
    test('devrait avoir tous les types de métriques définis', () => {
      expect(METRIC_TYPES.CRITICAL_STOCK_COUNT).toBe('custom.googleapis.com/sigma/critical_stock_count');
      expect(METRIC_TYPES.CRITICAL_OVERDUE_COUNT).toBe('custom.googleapis.com/sigma/critical_overdue_count');
      expect(METRIC_TYPES.DASHBOARD_QUERY_DURATION).toBe('custom.googleapis.com/sigma/dashboard_query_duration');
      expect(METRIC_TYPES.DASHBOARD_ERROR_RATE).toBe('custom.googleapis.com/sigma/dashboard_error_rate');
      expect(METRIC_TYPES.FIRESTORE_READ_RATE).toBe('custom.googleapis.com/sigma/firestore_read_rate');
    });
  });
});
