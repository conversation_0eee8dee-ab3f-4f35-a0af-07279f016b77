/**
 * Gestionnaire de l'interface utilisateur du dashboard SIGMA
 * Mise à jour des tableaux et gestion des interactions
 */

class DashboardUI {
  constructor() {
    this.tableRenderers = new Map();
    this.sortStates = new Map();
    this.filterStates = new Map();
    
    this.initializeTableRenderers();
  }

  /**
   * Initialiser les renderers pour chaque tableau
   */
  initializeTableRenderers() {
    this.tableRenderers.set('stockAlerts', this.renderStockAlertsTable.bind(this));
    this.tableRenderers.set('missingMaterial', this.renderMissingMaterialTable.bind(this));
    this.tableRenderers.set('overdueEmprunts', this.renderOverdueEmpruntsTable.bind(this));
    this.tableRenderers.set('upcomingEmprunts', this.renderUpcomingEmpruntsTable.bind(this));
    this.tableRenderers.set('nonOpModules', this.renderNonOpModulesTable.bind(this));
    this.tableRenderers.set('nonOpMaterial', this.renderNonOpMaterialTable.bind(this));
    this.tableRenderers.set('pendingEmprunts', this.renderPendingEmpruntsTable.bind(this));
  }

  /**
   * Mettre à jour le tableau des alertes stock
   */
  updateStockAlertsTable(alerts) {
    const container = document.getElementById('stock-alerts-table');
    if (!container) return;

    if (alerts.length === 0) {
      container.innerHTML = this.renderEmptyState('Aucune alerte stock', '✅ Tous les stocks sont au-dessus du seuil');
      return;
    }

    const html = `
      <div class="table-header">
        <h4>🚨 Alertes Stock (${alerts.length})</h4>
        <button class="btn-refresh" onclick="dashboardManager.refreshTable('stockAlerts')">🔄</button>
      </div>
      <div class="table-responsive">
        <table class="dashboard-table">
          <thead>
            <tr>
              <th>Article</th>
              <th>Stock</th>
              <th>Seuil</th>
              <th>%</th>
              <th>Localisation</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${alerts.map(alert => this.renderStockAlertRow(alert)).join('')}
          </tbody>
        </table>
      </div>
    `;

    container.innerHTML = html;
    this.addTableInteractions(container);
  }

  /**
   * Rendre une ligne d'alerte stock
   */
  renderStockAlertRow(alert) {
    const percentage = Math.round((alert.quantite / alert.seuilAlerte) * 100);
    const severity = this.getStockSeverity(percentage);
    
    return `
      <tr class="alert-row ${severity}">
        <td>
          <strong>${alert.nom}</strong>
          <br><small>${alert.categorie || 'Général'}</small>
        </td>
        <td class="text-center">
          <span class="stock-quantity ${severity}">${alert.quantite}</span>
        </td>
        <td class="text-center">${alert.seuilAlerte}</td>
        <td class="text-center">
          <span class="percentage ${severity}">${percentage}%</span>
        </td>
        <td>${alert.localisation || 'Non spécifiée'}</td>
        <td>
          <button class="btn btn-sm btn-primary" onclick="dashboardUI.orderStock('${alert.id}')">
            📦 Commander
          </button>
        </td>
      </tr>
    `;
  }

  /**
   * Mettre à jour le tableau du matériel manquant
   */
  updateMissingMaterialTable(materials) {
    const container = document.getElementById('missing-material-table');
    if (!container) return;

    if (materials.length === 0) {
      container.innerHTML = this.renderEmptyState('Aucun matériel à commander', '✅ Tous les stocks sont disponibles');
      return;
    }

    const html = `
      <div class="table-header">
        <h4>❌ Matériel à Commander (${materials.length})</h4>
        <button class="btn-refresh" onclick="dashboardManager.refreshTable('missingMaterial')">🔄</button>
      </div>
      <div class="table-responsive">
        <table class="dashboard-table">
          <thead>
            <tr>
              <th>Article</th>
              <th>Catégorie</th>
              <th>Fournisseur</th>
              <th>Prix Unit.</th>
              <th>Dernière MAJ</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${materials.map(material => this.renderMissingMaterialRow(material)).join('')}
          </tbody>
        </table>
      </div>
    `;

    container.innerHTML = html;
    this.addTableInteractions(container);
  }

  /**
   * Rendre une ligne de matériel manquant
   */
  renderMissingMaterialRow(material) {
    const updatedAt = material.updatedAt ? new Date(material.updatedAt.seconds * 1000) : new Date();
    
    return `
      <tr>
        <td>
          <strong>${material.nom}</strong>
          <br><small>${material.description || ''}</small>
        </td>
        <td>${material.categorie || 'Général'}</td>
        <td>${material.refFournisseur || 'Non spécifié'}</td>
        <td class="text-right">
          ${material.prixUnitaire ? `${material.prixUnitaire.toFixed(2)}€` : 'N/A'}
        </td>
        <td>${this.formatRelativeTime(updatedAt)}</td>
        <td>
          <button class="btn btn-sm btn-success" onclick="dashboardUI.createOrder('${material.id}')">
            🛒 Commander
          </button>
        </td>
      </tr>
    `;
  }

  /**
   * Mettre à jour le tableau des emprunts en retard
   */
  updateOverdueEmpruntsTable(emprunts) {
    const container = document.getElementById('overdue-emprunts-table');
    if (!container) return;

    if (emprunts.length === 0) {
      container.innerHTML = this.renderEmptyState('Aucun emprunt en retard', '✅ Tous les emprunts sont à jour');
      return;
    }

    const html = `
      <div class="table-header">
        <h4>⏰ Emprunts en Retard (${emprunts.length})</h4>
        <button class="btn-refresh" onclick="dashboardManager.refreshTable('overdueEmprunts')">🔄</button>
      </div>
      <div class="table-responsive">
        <table class="dashboard-table">
          <thead>
            <tr>
              <th>Emprunt</th>
              <th>Emprunteur</th>
              <th>Lieu</th>
              <th>Retour Prévu</th>
              <th>Retard</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${emprunts.map(emprunt => this.renderOverdueEmpruntRow(emprunt)).join('')}
          </tbody>
        </table>
      </div>
    `;

    container.innerHTML = html;
    this.addTableInteractions(container);
  }

  /**
   * Rendre une ligne d'emprunt en retard
   */
  renderOverdueEmpruntRow(emprunt) {
    const priority = this.getOverduePriority(emprunt.daysOverdue);
    
    return `
      <tr class="overdue-row ${priority}">
        <td>
          <strong>${emprunt.nom}</strong>
          <br><small>${emprunt.secteur || 'Non spécifié'}</small>
        </td>
        <td>${emprunt.emprunteur}</td>
        <td>${emprunt.lieu}</td>
        <td>${this.formatDate(emprunt.dateRetourPrevue)}</td>
        <td class="text-center">
          <span class="overdue-badge ${priority}">
            ${emprunt.daysOverdue} jour${emprunt.daysOverdue > 1 ? 's' : ''}
          </span>
        </td>
        <td>
          <button class="btn btn-sm btn-warning" onclick="dashboardUI.sendReminder('${emprunt.id}')">
            📧 Rappel
          </button>
          <button class="btn btn-sm btn-info" onclick="dashboardUI.viewEmprunt('${emprunt.id}')">
            👁️ Voir
          </button>
        </td>
      </tr>
    `;
  }

  /**
   * Rendre un état vide pour un tableau
   */
  renderEmptyState(title, message) {
    return `
      <div class="empty-state">
        <div class="empty-icon">📊</div>
        <h4>${title}</h4>
        <p>${message}</p>
      </div>
    `;
  }

  /**
   * Ajouter les interactions aux tableaux
   */
  addTableInteractions(container) {
    // Ajouter le tri sur les colonnes
    const headers = container.querySelectorAll('th[data-sort]');
    headers.forEach(header => {
      header.addEventListener('click', () => {
        this.handleSort(header);
      });
    });

    // Ajouter les filtres
    const filterInputs = container.querySelectorAll('.filter-input');
    filterInputs.forEach(input => {
      input.addEventListener('input', (e) => {
        this.handleFilter(e.target);
      });
    });
  }

  /**
   * Gérer le tri des colonnes
   */
  handleSort(header) {
    const tableId = header.closest('.dashboard-table').id;
    const sortKey = header.dataset.sort;
    
    let currentSort = this.sortStates.get(tableId) || { key: null, direction: 'asc' };
    
    if (currentSort.key === sortKey) {
      currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
      currentSort = { key: sortKey, direction: 'asc' };
    }
    
    this.sortStates.set(tableId, currentSort);
    
    // Mettre à jour l'affichage du tri
    this.updateSortIndicators(header.closest('table'), currentSort);
    
    // Déclencher le tri
    this.sortTable(tableId, currentSort);
  }

  /**
   * Obtenir la sévérité d'une alerte stock
   */
  getStockSeverity(percentage) {
    if (percentage <= 25) return 'critical';
    if (percentage <= 50) return 'warning';
    return 'info';
  }

  /**
   * Obtenir la priorité d'un emprunt en retard
   */
  getOverduePriority(daysOverdue) {
    if (daysOverdue > 7) return 'high';
    if (daysOverdue >= 3) return 'medium';
    return 'low';
  }

  /**
   * Formater une date relative
   */
  formatRelativeTime(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Aujourd\'hui';
    if (diffDays === 1) return 'Hier';
    if (diffDays < 7) return `Il y a ${diffDays} jours`;
    if (diffDays < 30) return `Il y a ${Math.floor(diffDays / 7)} semaines`;
    return `Il y a ${Math.floor(diffDays / 30)} mois`;
  }

  /**
   * Formater une date
   */
  formatDate(date) {
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  /**
   * Actions utilisateur - Commander du stock
   */
  orderStock(stockId) {
    console.log(`📦 Commande de stock: ${stockId}`);
    // Ici, on pourrait ouvrir un modal de commande
    alert(`Fonctionnalité de commande pour le stock ${stockId} à implémenter`);
  }

  /**
   * Actions utilisateur - Créer une commande
   */
  createOrder(materialId) {
    console.log(`🛒 Création de commande: ${materialId}`);
    // Ici, on pourrait ouvrir un formulaire de commande
    alert(`Fonctionnalité de création de commande pour ${materialId} à implémenter`);
  }

  /**
   * Actions utilisateur - Envoyer un rappel
   */
  sendReminder(empruntId) {
    console.log(`📧 Envoi de rappel: ${empruntId}`);
    // Ici, on pourrait appeler une Cloud Function
    alert(`Fonctionnalité d'envoi de rappel pour l'emprunt ${empruntId} à implémenter`);
  }

  /**
   * Actions utilisateur - Voir un emprunt
   */
  viewEmprunt(empruntId) {
    console.log(`👁️ Affichage emprunt: ${empruntId}`);
    // Ici, on pourrait ouvrir un modal de détails
    alert(`Fonctionnalité d'affichage des détails de l'emprunt ${empruntId} à implémenter`);
  }

  /**
   * Mettre à jour le tableau des prochains emprunts
   */
  updateUpcomingEmpruntsTable(emprunts) {
    const container = document.getElementById('upcoming-emprunts-table');
    if (!container) return;

    if (emprunts.length === 0) {
      container.innerHTML = this.renderEmptyState('Aucun emprunt prévu', '📅 Pas d\'emprunt dans les 30 prochains jours');
      return;
    }

    const html = `
      <div class="table-header">
        <h4>📅 Prochains Emprunts (${emprunts.length})</h4>
        <button class="btn-refresh" onclick="dashboardManager.refreshTable('upcomingEmprunts')">🔄</button>
      </div>
      <div class="table-responsive">
        <table class="dashboard-table">
          <thead>
            <tr>
              <th>Emprunt</th>
              <th>Emprunteur</th>
              <th>Lieu</th>
              <th>Départ</th>
              <th>Statut</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${emprunts.map(emprunt => this.renderUpcomingEmpruntRow(emprunt)).join('')}
          </tbody>
        </table>
      </div>
    `;

    container.innerHTML = html;
    this.addTableInteractions(container);
  }

  /**
   * Rendre une ligne d'emprunt à venir
   */
  renderUpcomingEmpruntRow(emprunt) {
    const daysUntilDeparture = Math.ceil((emprunt.dateDepart - new Date()) / (1000 * 60 * 60 * 24));
    const urgency = daysUntilDeparture <= 7 ? 'urgent' : daysUntilDeparture <= 14 ? 'soon' : 'normal';

    return `
      <tr class="upcoming-row ${urgency}">
        <td>
          <strong>${emprunt.nom}</strong>
          <br><small>${emprunt.secteur || 'Non spécifié'}</small>
        </td>
        <td>${emprunt.emprunteur}</td>
        <td>${emprunt.lieu}</td>
        <td>
          ${this.formatDate(emprunt.dateDepart)}
          <br><small class="days-until ${urgency}">Dans ${daysUntilDeparture} jour${daysUntilDeparture > 1 ? 's' : ''}</small>
        </td>
        <td>
          <span class="status-badge ${emprunt.statut.toLowerCase().replace(' ', '-')}">${emprunt.statut}</span>
        </td>
        <td>
          <button class="btn btn-sm btn-info" onclick="dashboardUI.viewEmprunt('${emprunt.id}')">
            👁️ Voir
          </button>
        </td>
      </tr>
    `;
  }

  /**
   * Mettre à jour le tableau des modules non opérationnels
   */
  updateNonOpModulesTable(modules) {
    const container = document.getElementById('non-op-modules-table');
    if (!container) return;

    if (modules.length === 0) {
      container.innerHTML = this.renderEmptyState('Aucun module non opérationnel', '✅ Tous les modules sont prêts');
      return;
    }

    const html = `
      <div class="table-header">
        <h4>📌 Modules Non Opérationnels (${modules.length})</h4>
        <button class="btn-refresh" onclick="dashboardManager.refreshTable('nonOpModules')">🔄</button>
      </div>
      <div class="table-responsive">
        <table class="dashboard-table">
          <thead>
            <tr>
              <th>Module</th>
              <th>Type</th>
              <th>Localisation</th>
              <th>Dernière MAJ</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${modules.map(module => this.renderNonOpModuleRow(module)).join('')}
          </tbody>
        </table>
      </div>
    `;

    container.innerHTML = html;
    this.addTableInteractions(container);
  }

  /**
   * Rendre une ligne de module non opérationnel
   */
  renderNonOpModuleRow(module) {
    const updatedAt = module.updatedAt ? new Date(module.updatedAt.seconds * 1000) : new Date();

    return `
      <tr>
        <td>
          <strong>${module.nom}</strong>
          <br><small>${module.description || ''}</small>
        </td>
        <td>${module.type || 'Module'}</td>
        <td>${module.localisation || 'Non spécifiée'}</td>
        <td>${this.formatRelativeTime(updatedAt)}</td>
        <td>
          <button class="btn btn-sm btn-warning" onclick="dashboardUI.repairModule('${module.id}')">
            🔧 Réparer
          </button>
        </td>
      </tr>
    `;
  }

  /**
   * Mettre à jour le tableau du matériel non opérationnel
   */
  updateNonOpMaterialTable(materials) {
    const container = document.getElementById('non-op-material-table');
    if (!container) return;

    if (materials.length === 0) {
      container.innerHTML = this.renderEmptyState('Aucun matériel non opérationnel', '✅ Tout le matériel est opérationnel');
      return;
    }

    const html = `
      <div class="table-header">
        <h4>🔧 Matériel Non Opérationnel (${materials.length})</h4>
        <button class="btn-refresh" onclick="dashboardManager.refreshTable('nonOpMaterial')">🔄</button>
      </div>
      <div class="table-responsive">
        <table class="dashboard-table">
          <thead>
            <tr>
              <th>Article</th>
              <th>Catégorie</th>
              <th>Problème</th>
              <th>Dernière MAJ</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${materials.map(material => this.renderNonOpMaterialRow(material)).join('')}
          </tbody>
        </table>
      </div>
    `;

    container.innerHTML = html;
    this.addTableInteractions(container);
  }

  /**
   * Rendre une ligne de matériel non opérationnel
   */
  renderNonOpMaterialRow(material) {
    const updatedAt = material.updatedAt ? new Date(material.updatedAt.seconds * 1000) : new Date();

    return `
      <tr>
        <td>
          <strong>${material.nom}</strong>
          <br><small>${material.description || ''}</small>
        </td>
        <td>${material.categorie || 'Général'}</td>
        <td>${material.probleme || 'Non spécifié'}</td>
        <td>${this.formatRelativeTime(updatedAt)}</td>
        <td>
          <button class="btn btn-sm btn-warning" onclick="dashboardUI.repairMaterial('${material.id}')">
            🔧 Réparer
          </button>
        </td>
      </tr>
    `;
  }

  /**
   * Mettre à jour le tableau des emprunts en attente
   */
  updatePendingEmpruntsTable(emprunts) {
    const container = document.getElementById('pending-emprunts-table');
    if (!container) return;

    if (emprunts.length === 0) {
      container.innerHTML = this.renderEmptyState('Aucun emprunt en attente', '✅ Tous les emprunts sont traités');
      return;
    }

    const html = `
      <div class="table-header">
        <h4>📋 Emprunts en Attente (${emprunts.length})</h4>
        <button class="btn-refresh" onclick="dashboardManager.refreshTable('pendingEmprunts')">🔄</button>
      </div>
      <div class="table-responsive">
        <table class="dashboard-table">
          <thead>
            <tr>
              <th>Emprunt</th>
              <th>Emprunteur</th>
              <th>Type d'attente</th>
              <th>Retour</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${emprunts.map(emprunt => this.renderPendingEmpruntRow(emprunt)).join('')}
          </tbody>
        </table>
      </div>
    `;

    container.innerHTML = html;
    this.addTableInteractions(container);
  }

  /**
   * Rendre une ligne d'emprunt en attente
   */
  renderPendingEmpruntRow(emprunt) {
    const dateRetour = emprunt.dateRetourEffective ? new Date(emprunt.dateRetourEffective.seconds * 1000) : new Date();

    return `
      <tr>
        <td>
          <strong>${emprunt.nom}</strong>
          <br><small>${emprunt.lieu}</small>
        </td>
        <td>${emprunt.emprunteur}</td>
        <td>
          <span class="pending-type ${emprunt.pendingType}">${emprunt.pendingType}</span>
        </td>
        <td>${this.formatDate(dateRetour)}</td>
        <td>
          ${emprunt.pendingType === 'inventaire' ?
            `<button class="btn btn-sm btn-success" onclick="dashboardUI.processInventory('${emprunt.id}')">📋 Inventorier</button>` :
            `<button class="btn btn-sm btn-primary" onclick="dashboardUI.processBilling('${emprunt.id}')">💰 Facturer</button>`
          }
        </td>
      </tr>
    `;
  }

  /**
   * Actions utilisateur - Réparer un module
   */
  repairModule(moduleId) {
    console.log(`🔧 Réparation module: ${moduleId}`);
    alert(`Fonctionnalité de réparation pour le module ${moduleId} à implémenter`);
  }

  /**
   * Actions utilisateur - Réparer du matériel
   */
  repairMaterial(materialId) {
    console.log(`🔧 Réparation matériel: ${materialId}`);
    alert(`Fonctionnalité de réparation pour le matériel ${materialId} à implémenter`);
  }

  /**
   * Actions utilisateur - Traiter l'inventaire
   */
  processInventory(empruntId) {
    console.log(`📋 Traitement inventaire: ${empruntId}`);
    alert(`Fonctionnalité de traitement d'inventaire pour l'emprunt ${empruntId} à implémenter`);
  }

  /**
   * Actions utilisateur - Traiter la facturation
   */
  processBilling(empruntId) {
    console.log(`💰 Traitement facturation: ${empruntId}`);
    alert(`Fonctionnalité de traitement de facturation pour l'emprunt ${empruntId} à implémenter`);
  }
}

// Export pour utilisation globale
window.DashboardUI = DashboardUI;
