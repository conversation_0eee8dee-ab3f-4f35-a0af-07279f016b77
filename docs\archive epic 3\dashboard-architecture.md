# Architecture Dashboard SIGMA - Épique E-3

*Version : 1.0 - Conception détaillée*  
*Auteur : Agent IA - Mission E-3*

## 1. Vue d'ensemble

Le dashboard SIGMA centralise 7 tableaux temps-réel pour optimiser la gestion logistique :

1. **Alertes Stock** - Articles critiques (quantité < seuil)
2. **Matériel spécifique manquant** - Articles à commander  
3. **Emprunts non revenus** - Retards et dépassements
4. **Prochains emprunts** - Départs < 30 jours
5. **Modules non opérationnels** - Malles à réassortir
6. **Matériel non opérationnel** - En réparation/dégradé
7. **Emprunts en attente** - Non inventoriés/facturés

## 2. Architecture technique

### 2.1 Backend - Cloud Functions

#### `getDashboardData` - Agrégation optimisée
```typescript
// functions/src/dashboard/getDashboardData.ts
export const getDashboardData = functions.https.onCall(async (data, context) => {
  checkRegisseurOrAdmin(context);
  
  const db = getFirestore();
  const results = await Promise.all([
    getStockAlerts(db),
    getMissingMaterial(db), 
    getOverdueEmprunts(db),
    getUpcomingEmprunts(db),
    getNonOperationalModules(db),
    getNonOperationalMaterial(db),
    getPendingEmprunts(db)
  ]);
  
  return {
    stockAlerts: results[0],
    missingMaterial: results[1],
    overdueEmprunts: results[2],
    upcomingEmprunts: results[3],
    nonOpModules: results[4],
    nonOpMaterial: results[5],
    pendingEmprunts: results[6],
    timestamp: new Date().toISOString()
  };
});
```

#### Requêtes Firestore optimisées

**1. Alertes Stock** (quantité < seuil)
```typescript
async function getStockAlerts(db: FirebaseFirestore.Firestore) {
  return db.collection('stocks')
    .where('quantite', '<=', 'seuilAlerte')
    .where('estOperationnel', '==', true)
    .orderBy('quantite', 'asc')
    .limit(20)
    .get();
}
```

**2. Emprunts en retard** (statut "Parti" + date dépassée)
```typescript
async function getOverdueEmprunts(db: FirebaseFirestore.Firestore) {
  const now = new Date();
  return db.collection('emprunts')
    .where('statut', '==', 'Parti')
    .where('dateRetourPrevue', '<', now)
    .orderBy('dateRetourPrevue', 'asc')
    .limit(20)
    .get();
}
```

**3. Prochains emprunts** (départ < 30 jours)
```typescript
async function getUpcomingEmprunts(db: FirebaseFirestore.Firestore) {
  const now = new Date();
  const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
  
  return db.collection('emprunts')
    .where('statut', 'in', ['Pas prêt', 'Prêt'])
    .where('dateDepart', '>=', now)
    .where('dateDepart', '<=', in30Days)
    .orderBy('dateDepart', 'asc')
    .limit(20)
    .get();
}
```

### 2.2 Index Firestore requis

```json
{
  "indexes": [
    {
      "collectionGroup": "stocks",
      "queryScope": "COLLECTION", 
      "fields": [
        {"fieldPath": "quantite", "order": "ASCENDING"},
        {"fieldPath": "seuilAlerte", "order": "ASCENDING"},
        {"fieldPath": "estOperationnel", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "emprunts",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "statut", "order": "ASCENDING"},
        {"fieldPath": "dateRetourPrevue", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "emprunts", 
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "statut", "order": "ASCENDING"},
        {"fieldPath": "dateDepart", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "emprunts",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "estInventorie", "order": "ASCENDING"},
        {"fieldPath": "estFacture", "order": "ASCENDING"},
        {"fieldPath": "statut", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "modules",
      "queryScope": "COLLECTION", 
      "fields": [
        {"fieldPath": "estPret", "order": "ASCENDING"},
        {"fieldPath": "updatedAt", "order": "DESCENDING"}
      ]
    }
  ]
}
```

### 2.3 Frontend - Interface temps-réel

#### Structure HTML
```html
<!-- src/html/dashboard.html -->
<div id="dashboard-container">
  <div class="dashboard-header">
    <h1>Dashboard SIGMA</h1>
    <div class="refresh-indicator" id="refresh-indicator"></div>
  </div>
  
  <div class="dashboard-grid">
    <!-- 7 tableaux avec listeners temps-réel -->
    <div class="dashboard-card" id="stock-alerts">
      <h3>🚨 Alertes Stock</h3>
      <div class="table-container" id="stock-alerts-table"></div>
    </div>
    
    <div class="dashboard-card" id="overdue-emprunts">
      <h3>⏰ Emprunts en retard</h3>
      <div class="table-container" id="overdue-emprunts-table"></div>
    </div>
    
    <!-- ... 5 autres tableaux ... -->
  </div>
</div>
```

#### Listeners temps-réel avec pagination
```javascript
// src/js/dashboard/dashboard.js
class DashboardManager {
  constructor() {
    this.listeners = new Map();
    this.lastDocs = new Map();
    this.debounceTimers = new Map();
  }
  
  setupRealtimeListeners() {
    // Listener pour alertes stock
    this.setupStockAlertsListener();
    this.setupOverdueEmpruntsListener();
    // ... autres listeners
  }
  
  setupStockAlertsListener() {
    const query = db.collection('stocks')
      .where('quantite', '<=', firebase.firestore.FieldPath.documentId())
      .orderBy('quantite', 'asc')
      .limit(20);
      
    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleStockAlertsUpdate(snapshot),
      (error) => this.handleError('stock-alerts', error)
    );
    
    this.listeners.set('stock-alerts', unsubscribe);
  }
  
  handleStockAlertsUpdate(snapshot) {
    // Debouncing pour éviter trop de mises à jour
    this.debounceUpdate('stock-alerts', () => {
      const alerts = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      this.updateStockAlertsTable(alerts);
    }, 200);
  }
  
  debounceUpdate(key, callback, delay) {
    clearTimeout(this.debounceTimers.get(key));
    this.debounceTimers.set(key, setTimeout(callback, delay));
  }
}
```

## 3. Performance & Monitoring

### 3.1 Optimisations
- **Pagination** : `limit(20)` sur toutes les requêtes
- **Debouncing** : 200ms pour éviter surcharge UI
- **Cache offline** : Firestore cache pour PWA
- **Index composites** : Requêtes optimisées < 100 lectures/sec

### 3.2 Cloud Monitoring
```typescript
// functions/src/monitoring/setupAlerts.ts
export const setupDashboardAlerts = functions.https.onCall(async (data, context) => {
  checkRegisseurOrAdmin(context);
  
  // Alerte latence dashboard > 500ms
  // Alerte coût Firestore > seuil
  // Alerte erreurs > 5% 
});
```

## 4. Tests & Validation

### 4.1 Tests unitaires (Jest)
- Cloud Functions avec Firebase Emulator
- Mocks Firestore cohérents
- Validation performance requêtes

### 4.2 Tests E2E (Playwright)
- Navigation `/dashboard`
- Chargement 7 tableaux < 2s
- Filtres temps-réel < 200ms
- Simulation alertes

### 4.3 Audit Lighthouse
- Score performance ≥ 90
- Optimisations PWA
- Métriques Core Web Vitals

## 5. Sécurité

### 5.1 Authentification
- Validation rôles : `regisseur` ou `admin`
- Custom Claims Firebase Auth
- Règles Firestore basées sur `request.auth.token.role`

### 5.2 Données sensibles
- Pas de données personnelles exposées
- Logs structurés sans informations sensibles
- Rate limiting sur Cloud Functions

---

*Architecture validée selon patterns SIGMA existants*
