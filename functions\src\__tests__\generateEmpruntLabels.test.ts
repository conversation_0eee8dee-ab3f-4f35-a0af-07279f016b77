import { generateEmpruntLabels } from "../emprunts/generateEmpruntLabels";
import * as admin from "firebase-admin";
import { adminDb } from "../tests/setupTests";

describe("generateEmpruntLabels Cloud Function", () => {
  let testEnv: any;
  let wrapped: any;

  beforeAll(() => {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    testEnv = require("firebase-functions-test")();
    
    // Initialiser Firebase Admin est maintenant fait dans setupTests
    
    wrapped = testEnv.wrap(generateEmpruntLabels);
  });

  afterAll(() => {
    testEnv.cleanup();
  });

  test("devrait générer des étiquettes avec succès", async () => {
    const data = {
      empruntId: "test-emprunt-id"
    };

    const context = {
      auth: {
        uid: "test-uid",
        token: {
          email: "<EMAIL>",
          role: "admin"
        }
      }
    };

    const result = await wrapped(data, context);
    expect(result).toHaveProperty("success", true);
    expect(result).toHaveProperty("pdf");
    expect(result).toHaveProperty("filename");
  });
});
