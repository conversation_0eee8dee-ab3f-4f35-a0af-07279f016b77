/**
 * Script de validation des index Firestore pour le dashboard SIGMA
 * Vérifie la performance des requêtes et l'utilisation des index
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Configuration Firebase pour les tests
const serviceAccount = process.env.GOOGLE_APPLICATION_CREDENTIALS 
  ? JSON.parse(fs.readFileSync(process.env.GOOGLE_APPLICATION_CREDENTIALS, 'utf8'))
  : null;

if (serviceAccount) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'sigma-nova'
  });
} else {
  // Mode émulateur
  process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
  admin.initializeApp({ projectId: 'sigma-nova' });
}

const db = admin.firestore();

/**
 * Requêtes dashboard à valider
 */
const DASHBOARD_QUERIES = {
  stockAlerts: {
    name: "Alertes Stock",
    query: () => db.collection('stocks')
      .where('estOperationnel', '==', true)
      .orderBy('quantite', 'asc')
      .limit(20),
    expectedIndex: "(quantite, seuilAlerte, estOperationnel)"
  },
  
  missingMaterial: {
    name: "Matériel Manquant", 
    query: () => db.collection('stocks')
      .where('aCommander', '==', true)
      .orderBy('updatedAt', 'desc')
      .limit(20),
    expectedIndex: "(aCommander, updatedAt)"
  },
  
  overdueEmprunts: {
    name: "Emprunts en Retard",
    query: () => db.collection('emprunts')
      .where('statut', '==', 'Parti')
      .where('dateRetourPrevue', '<', new Date())
      .orderBy('dateRetourPrevue', 'asc')
      .limit(20),
    expectedIndex: "(statut, dateRetourPrevue)"
  },
  
  upcomingEmprunts: {
    name: "Prochains Emprunts",
    query: () => {
      const now = new Date();
      const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      return db.collection('emprunts')
        .where('statut', 'in', ['Pas prêt', 'Prêt'])
        .where('dateDepart', '>=', now)
        .where('dateDepart', '<=', in30Days)
        .orderBy('dateDepart', 'asc')
        .limit(20);
    },
    expectedIndex: "(statut, dateDepart)"
  },
  
  nonOpModules: {
    name: "Modules Non Opérationnels",
    query: () => db.collection('modules')
      .where('estPret', '==', false)
      .orderBy('updatedAt', 'desc')
      .limit(20),
    expectedIndex: "(estPret, updatedAt)"
  },
  
  nonOpMaterial: {
    name: "Matériel Non Opérationnel",
    query: () => db.collection('stocks')
      .where('estOperationnel', '==', false)
      .orderBy('updatedAt', 'desc')
      .limit(20),
    expectedIndex: "(estOperationnel, updatedAt)"
  },
  
  pendingEmpruntsInventory: {
    name: "Emprunts Non Inventoriés",
    query: () => db.collection('emprunts')
      .where('statut', '==', 'Revenu')
      .where('estInventorie', '==', false)
      .orderBy('dateRetourEffective', 'desc')
      .limit(10),
    expectedIndex: "(estInventorie, estFacture, statut)"
  }
};

/**
 * Valider une requête et mesurer sa performance
 */
async function validateQuery(queryName, queryConfig) {
  console.log(`\n🔍 Validation: ${queryConfig.name}`);
  
  try {
    const startTime = Date.now();
    
    // Exécuter la requête
    const snapshot = await queryConfig.query().get();
    
    const executionTime = Date.now() - startTime;
    const docCount = snapshot.docs.length;
    
    // Analyser les métriques
    const metrics = {
      executionTime,
      docCount,
      avgTimePerDoc: docCount > 0 ? executionTime / docCount : 0,
      usesIndex: executionTime < 100 // Heuristique simple
    };
    
    // Validation des critères de performance
    const isValid = {
      executionTime: executionTime < 500, // < 500ms
      docLimit: docCount <= 20,           // Limite respectée
      usesIndex: metrics.usesIndex        // Utilise un index
    };
    
    const allValid = Object.values(isValid).every(v => v);
    
    console.log(`  ⏱️  Temps d'exécution: ${executionTime}ms ${isValid.executionTime ? '✅' : '❌'}`);
    console.log(`  📄 Documents récupérés: ${docCount} ${isValid.docLimit ? '✅' : '❌'}`);
    console.log(`  🔗 Index utilisé: ${metrics.usesIndex ? 'Oui' : 'Non'} ${isValid.usesIndex ? '✅' : '❌'}`);
    console.log(`  📊 Index attendu: ${queryConfig.expectedIndex}`);
    console.log(`  ✅ Validation globale: ${allValid ? 'SUCCÈS' : 'ÉCHEC'}`);
    
    return {
      queryName,
      metrics,
      isValid,
      allValid,
      expectedIndex: queryConfig.expectedIndex
    };
    
  } catch (error) {
    console.log(`  ❌ Erreur: ${error.message}`);
    
    // Analyser le type d'erreur
    if (error.message.includes('index')) {
      console.log(`  🔧 Solution: Créer l'index composite ${queryConfig.expectedIndex}`);
    }
    
    return {
      queryName,
      error: error.message,
      allValid: false,
      expectedIndex: queryConfig.expectedIndex
    };
  }
}

/**
 * Créer des données de test si nécessaire
 */
async function createTestData() {
  console.log('\n📝 Création de données de test...');
  
  try {
    // Vérifier si des données existent déjà
    const stocksSnapshot = await db.collection('stocks').limit(1).get();
    
    if (!stocksSnapshot.empty) {
      console.log('  ✅ Données existantes détectées, pas de création nécessaire');
      return;
    }
    
    // Créer quelques documents de test
    const batch = db.batch();
    
    // Stocks de test
    for (let i = 1; i <= 5; i++) {
      const stockRef = db.collection('stocks').doc(`test-stock-${i}`);
      batch.set(stockRef, {
        nom: `Stock Test ${i}`,
        quantite: i * 2,
        seuilAlerte: 10,
        estOperationnel: i % 2 === 0,
        aCommander: i === 1,
        updatedAt: new Date(),
        createdAt: new Date()
      });
    }
    
    // Emprunts de test
    for (let i = 1; i <= 5; i++) {
      const empruntRef = db.collection('emprunts').doc(`test-emprunt-${i}`);
      const dateRetour = new Date();
      dateRetour.setDate(dateRetour.getDate() + (i - 3)); // Certains en retard
      
      batch.set(empruntRef, {
        nom: `Emprunt Test ${i}`,
        statut: i <= 2 ? 'Parti' : 'Prêt',
        dateRetourPrevue: dateRetour,
        dateDepart: new Date(),
        estInventorie: i % 2 === 0,
        estFacture: i % 3 === 0,
        emprunteur: `Utilisateur ${i}`,
        createdAt: new Date()
      });
    }
    
    // Modules de test
    for (let i = 1; i <= 3; i++) {
      const moduleRef = db.collection('modules').doc(`test-module-${i}`);
      batch.set(moduleRef, {
        nom: `Module Test ${i}`,
        estPret: i !== 1,
        updatedAt: new Date(),
        createdAt: new Date()
      });
    }
    
    await batch.commit();
    console.log('  ✅ Données de test créées avec succès');
    
  } catch (error) {
    console.log(`  ⚠️  Erreur lors de la création des données: ${error.message}`);
  }
}

/**
 * Générer un rapport de validation
 */
function generateReport(results) {
  console.log('\n📊 RAPPORT DE VALIDATION DES INDEX FIRESTORE');
  console.log('='.repeat(60));
  
  const totalQueries = results.length;
  const successfulQueries = results.filter(r => r.allValid).length;
  const failedQueries = results.filter(r => !r.allValid).length;
  
  console.log(`\n📈 Résumé global:`);
  console.log(`  Total requêtes testées: ${totalQueries}`);
  console.log(`  Succès: ${successfulQueries} ✅`);
  console.log(`  Échecs: ${failedQueries} ${failedQueries > 0 ? '❌' : '✅'}`);
  console.log(`  Taux de réussite: ${Math.round((successfulQueries / totalQueries) * 100)}%`);
  
  if (failedQueries > 0) {
    console.log(`\n❌ Requêtes en échec:`);
    results.filter(r => !r.allValid).forEach(result => {
      console.log(`  - ${result.queryName}: ${result.error || 'Performance insuffisante'}`);
      console.log(`    Index requis: ${result.expectedIndex}`);
    });
  }
  
  const avgExecutionTime = results
    .filter(r => r.metrics)
    .reduce((sum, r) => sum + r.metrics.executionTime, 0) / 
    results.filter(r => r.metrics).length;
    
  console.log(`\n⏱️  Performance moyenne: ${Math.round(avgExecutionTime)}ms`);
  
  // Recommandations
  console.log(`\n💡 Recommandations:`);
  if (failedQueries === 0) {
    console.log(`  ✅ Tous les index sont optimisés !`);
    console.log(`  ✅ Performance < 100 lectures/sec garantie`);
  } else {
    console.log(`  🔧 Déployer les index manquants: firebase deploy --only firestore:indexes`);
    console.log(`  ⏳ Attendre la création des index (10-30 minutes)`);
    console.log(`  🔄 Relancer la validation après création`);
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 VALIDATION DES INDEX FIRESTORE DASHBOARD SIGMA');
  console.log('='.repeat(60));
  
  try {
    // Créer des données de test si nécessaire
    await createTestData();
    
    // Valider toutes les requêtes
    const results = [];
    
    for (const [queryName, queryConfig] of Object.entries(DASHBOARD_QUERIES)) {
      const result = await validateQuery(queryName, queryConfig);
      results.push(result);
    }
    
    // Générer le rapport
    generateReport(results);
    
    // Code de sortie
    const allSuccess = results.every(r => r.allValid);
    process.exit(allSuccess ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  main();
}

module.exports = { validateQuery, createTestData, DASHBOARD_QUERIES };
