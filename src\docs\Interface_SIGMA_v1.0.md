# Structure d’Interface – SIGMA
*Version : 1.1 – mis à jour le 10 July 2025*  
*Auteur : Aymeric*

> **Objectif**  
> Décrire une interface utilisateur **PWA** moderne, réactive et **Firebase‑native**, prête à être implémentée (ou générée) par un agent IA.  
> Chaque section précise : **but fonctionnel**, **composants/écrans**, **flux d’interaction**, hooks Firebase et KPI UX.

---

## 1 · Dashboard « Résumé »

- **Alertes**  
  Stocks bas, matériel manquant, modules en maintenance  
  (Hooks Firebase : `onSnapshot` collection `stocks` + Cloud Messaging pour les alertes critiques)
- **Emprunts prioritaires**  
  Overdue, départs H‑24, attente facture  
  (Query composite `status+dateRetour`)
- **Actions rapides**  
  Nouvel emprunt, Réassort, Modules KO, Facturation  
  (Boutons vers Callable CF)

> **UX KPI** : 1 s TTFB, 100 ms refresh RT, accessibilité offline (cache Firestore).

---

## 2 · Emprunts

### 2.1 Liste

- Table virtualisée (1000+ lignes sans jank).
- Filtres instantanés (statut, date, secteur).
- Colonnes : ID, Manip, Lieu, D Départ, D Retour, Statut / Couleur.
- Pagination adaptative : `startAfter` + 50 docs.

### 2.2 Détail

- **En‑tête** : Infos générales en temps réel (Listener doc `emprunts/<empruntId>`)
- **Contenu** : Modules & matériel liés (Sub‑collection `items`)
- **Historique** : Traçabilité (audit) (Sub‑collection `logs`)
- **Actions** : Départ, Retour, PDF Labels  
  (Callable CF `emprunts/checkout`, `emprunts/generateLabels`)

### 2.3 Wizard Création

1.  Base (nom, lieu, dates)
2.  Sélection matériel (suggestions AI, disponibilité temps réel)
3.  Livraison (optionnel)
4.  Validation (Transaction CF)

> **Autosave** brouillon `status=draft`.

---

## 3 · Modules

- **Liste** : Filtre statut, stats usage (via CF `modules/aggregateUsage`)
- **Fiche** : Contenu temps réel, démantèlement sécurisé, dépendances (emprunts actifs)
- **Démanteler / Dupliquer** : Transaction batch, mise à jour de l’index

---

## 4 · Stocks

### 4.1 Tableau stock

- Graphique seuils (Matplotlib JS)
- Prévision conso (via CF `stocks/predictUsage`)
- Filtres croisés (catégorie, fournisseur)

### 4.2 Fiche article

- Réf fournisseur, prix (`stocks/<stockId>`)
- Historique mouvements (Sub‑collection `mouvements`)
- QR Code (généré via CF + Storage)

---

## 5 · Livraisons

- Carte Leaflet (livraisons H‑7)
- Statut en temps réel (`livraisons/<livraisonId>`) + notifications
- Étape optimisation itinéraire (TSP API optionnel)

---

## 6 · Paramètres & Admin

- **Utilisateurs** : Rôles via Custom Claims
- **Fournisseurs** : CRUD + recherche
- **Seuils alertes** : Champ `settings/thresholds`
- **Mode maintenance** : Toggle `config/maintenance`

---

## 7 · Architecture UI (dossiers)

```text
src/
├── html/          # Templates GAS
│   ├── dashboard.html
│   ├── emprunts.html
│   └── …
├── js/
│   ├── main.js
│   ├── firebaseUtils.js
│   ├── components/     # Web components
│   │   ├── data-table.js
│   │   └── …
│   ├── views/
│   │   ├── dashboardView.js
│   │   ├── empruntsView.js
│   │   └── …
│   └── domain/        # Calls to CF / Firestore
│       ├── empruntsData.js
│       └── …
└── css/
    └── styles.css
````

### 7.1 Structure des Données Clés

* `emprunts/{empruntId}` -> sub-collections : items, historique
* `modules/{moduleId}`
* `stocks/{stockId}` -> sub-collection : mouvements

---

## 8 · Navigation & Recherche

* Sidebar hiérarchique avec badges d’alertes
* Fil d’Ariane et onglets récents
* Recherche globale (end‑point CF searchGlobal, Algolia optionnel)

---

## 9 · UX + PWA

* **Mode sombre** : CSS prefers‑color‑scheme
* **Offline** : Firestore cache + workbox
* **Notifications** : FCM + local dev token
* **Shortcuts clavier** : cmd+k search, n nouvel emprunt
* **Tableaux performants** : Tableaux virtualisés (>500 lignes)

---

## 10 · Sécurité & Performance

* Règles Firestore fines (role‑based)
* Transactions CF pour toute mutation critique
* Audit trail (sub‑collection logs)
* Score Lighthouse PWA > 90

---

## 11 · Fonctionnalités Clés Additionnelles

* Mode collaboratif : indication visuelle si un autre utilisateur consulte/modifie une fiche
* Commentaires : ajout de notes sur les emprunts avec mentions (@utilisateur)
* Vue Kanban : tableau visuel optionnel pour le suivi des emprunts par statut

---

Fin

