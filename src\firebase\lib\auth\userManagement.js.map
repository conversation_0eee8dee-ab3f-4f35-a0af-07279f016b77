{"version": 3, "file": "userManagement.js", "sourceRoot": "", "sources": ["../../src/auth/userManagement.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoUH,sCAaC;AA/UD,uDAAiE;AACjE,8CAA8C;AAC9C,wDAAwD;AACxD,kEAAoD;AACpD,6BAAwB;AAGxB,wBAAwB;AACxB,MAAM,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACjC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;CACnD,CAAC,CAAC;AAEH,MAAM,uBAAuB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;IAClD,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAClC,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;QACpB,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE;QACzC,KAAK,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE;QAC3C,aAAa,EAAE,OAAC,CAAC,MAAM,CAAC;YACtB,KAAK,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;YAC7B,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;YAC/B,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;YAChC,MAAM,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;YAC9B,UAAU,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;SACnC,CAAC,CAAC,QAAQ,EAAE;QACb,SAAS,EAAE,OAAC,CAAC,MAAM,CAAC;YAClB,WAAW,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;YACnC,WAAW,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;SACnE,CAAC,CAAC,QAAQ,EAAE;KACd,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH,MAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;IACxD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,EAAE,qBAAqB;IACxD,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC9D,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,gCAAgC;CACnE,CAAC,CAAC;AAMH;;GAEG;AACU,QAAA,WAAW,GAAG,IAAA,cAAM,EAC/B;IACE,MAAM,EAAE,cAAc;IACtB,YAAY,EAAE,EAAE;IAChB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,OAAO,EAAE,EAAE;;IAChB,IAAI,CAAC;QACH,qCAAqC;QACrC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,0BAA0B,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QAClC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAE3C,yBAAyB;QACzB,MAAM,EAAE,MAAM,EAAE,GAAG,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEzD,+BAA+B;QAC/B,0GAA0G;QAC1G,IAAI,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,kBAAU,CAAC,mBAAmB,EAAE,2BAA2B,CAAC,CAAC;QACzE,CAAC;QAED,4CAA4C;QAC5C,MAAM,IAAI,GAAG,IAAA,cAAO,GAAE,CAAC;QACvB,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;QAE1B,MAAM,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;YACtC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;SACzC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,kBAAU,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,IAAI,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzE,OAAO;YACL,GAAG,EAAE,QAAQ,CAAC,GAAG;YACjB,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,aAAa,EAAE,QAAQ,CAAC,aAAa;YACrC,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,aAAa;YACxC,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,YAAY;YACzC,cAAc,EAAE,QAAQ,CAAC,QAAQ,CAAC,cAAc;YAChD,QAAQ,EAAE,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,QAAQ,mCAAI,IAAI;YACzC,WAAW,EAAE,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,WAAW,KAAI,EAAE;YAC7C,KAAK,EAAE,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK,KAAI,EAAE;SAClC,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,kBAAU;YAAE,MAAM,KAAK,CAAC;QAE7C,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;YACxC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,QAAQ,EAAE,MAAA,OAAO,CAAC,IAAI,0CAAE,GAAG;SAC5B,CAAC,CAAC;QAEH,MAAM,IAAI,kBAAU,CAAC,UAAU,EAAE,2BAA2B,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;GAEG;AACU,QAAA,SAAS,GAAG,IAAA,cAAM,EAC7B;IACE,MAAM,EAAE,cAAc;IACtB,YAAY,EAAE,CAAC;IACf,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,OAAO,EAAE,EAAE;;IAChB,IAAI,CAAC;QACH,qCAAqC;QACrC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,0BAA0B,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAE3C,iEAAiE;QACjE,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACjD,MAAM,IAAI,kBAAU,CAAC,mBAAmB,EAAE,2BAA2B,CAAC,CAAC;QACzE,CAAC;QAED,yBAAyB;QACzB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpF,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;QAC1B,IAAI,KAAK,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAEhE,oBAAoB;QACpB,IAAI,IAAI,EAAE,CAAC;YACT,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,aAAa;QACb,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC;YACzE,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;gBACzB,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QAED,aAAa;QACb,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAE3B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;QACnC,MAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YAE5B,uEAAuE;YACvE,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,UAAU,GAAG,MAAA,QAAQ,CAAC,KAAK,0CAAE,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACvE,MAAM,SAAS,GAAG,MAAA,QAAQ,CAAC,WAAW,0CAAE,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAE5E,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC9B,SAAS;gBACX,CAAC;YACH,CAAC;YAED,KAAK,CAAC,IAAI,CAAC;gBACT,GAAG,EAAE,GAAG,CAAC,EAAE;gBACX,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,cAAc,EAAE,MAAA,QAAQ,CAAC,KAAK,0CAAE,cAAc;gBAC9C,aAAa,EAAE,CAAA,MAAA,QAAQ,CAAC,KAAK,0CAAE,aAAa,KAAI,CAAC;aAClD,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,KAAK;YACL,OAAO,EAAE,QAAQ,CAAC,IAAI,KAAK,KAAK;YAChC,UAAU,EAAE,MAAA,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,0CAAE,EAAE;SACxD,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,kBAAU;YAAE,MAAM,KAAK,CAAC;QAE7C,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;YACtC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,QAAQ,EAAE,MAAA,OAAO,CAAC,IAAI,0CAAE,GAAG;SAC5B,CAAC,CAAC;QAEH,MAAM,IAAI,kBAAU,CAAC,UAAU,EAAE,2BAA2B,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;GAEG;AACU,QAAA,iBAAiB,GAAG,IAAA,cAAM,EACrC;IACE,MAAM,EAAE,cAAc;IACtB,YAAY,EAAE,EAAE;IAChB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,OAAO,EAAE,EAAE;;IAChB,IAAI,CAAC;QACH,qCAAqC;QACrC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,0BAA0B,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QAClC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAE3C,yBAAyB;QACzB,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEzF,+BAA+B;QAC/B,+FAA+F;QAC/F,IAAI,QAAQ,KAAK,MAAM,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;YAClD,MAAM,IAAI,kBAAU,CAAC,mBAAmB,EAAE,2BAA2B,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,IAAI,GAAG,IAAA,cAAO,GAAE,CAAC;QACvB,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;QAE1B,yCAAyC;QACzC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;;YAC3D,oCAAoC;YACpC,MAAM,OAAO,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAE/C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACpB,MAAM,IAAI,kBAAU,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,OAAO,GAAQ;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,QAAQ;aACpB,CAAC;YAEF,oDAAoD;YACpD,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9B,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;gBAC/C,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;YACpC,CAAC;YAED,8BAA8B;YAC9B,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,YAAY,GAAG,CAAA,MAAA,OAAO,CAAC,IAAI,EAAE,0CAAE,WAAW,KAAI,EAAE,CAAC;gBACvD,OAAO,CAAC,WAAW,iDACd,YAAY,GACZ,WAAW;oBACd,6BAA6B;oBAC7B,aAAa,kCACR,YAAY,CAAC,aAAa,GAC1B,WAAW,CAAC,aAAa,GAE9B,SAAS,kCACJ,YAAY,CAAC,SAAS,GACtB,WAAW,CAAC,SAAS,IAE3B,CAAC;YACJ,CAAC;YAED,0CAA0C;YAC1C,OAAO,CAAC,sBAAsB,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7C,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAErC,OAAO;gBACL,GAAG,EAAE,MAAM;gBACX,WAAW,EAAE,WAAW,KAAI,MAAA,OAAO,CAAC,IAAI,EAAE,0CAAE,WAAW,CAAA;gBACvD,WAAW,EAAE,OAAO,CAAC,WAAW,KAAI,MAAA,OAAO,CAAC,IAAI,EAAE,0CAAE,WAAW,CAAA;aAChE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;YAC7C,MAAM;YACN,SAAS,EAAE,QAAQ;YACnB,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC;SAC9D,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,+BAA+B;YACxC,IAAI,EAAE,MAAM;SACb,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,kBAAU;YAAE,MAAM,KAAK,CAAC;QAE7C,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;YAC9C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,QAAQ,EAAE,MAAA,OAAO,CAAC,IAAI,0CAAE,GAAG;SAC5B,CAAC,CAAC;QAEH,MAAM,IAAI,kBAAU,CAAC,UAAU,EAAE,2BAA2B,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,aAAa,CAAC,QAAgB,EAAE,MAAc,EAAE,YAAqB,EAAE,QAAiB;IACtG,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,WAAW;YACd,OAAO,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,WAAW,IAAI,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC;QACzF,KAAK,WAAW;YACd,OAAO,QAAQ,KAAK,OAAO,IAAI,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC;QAC7D,KAAK,YAAY;YACf,OAAO,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,WAAW,CAAC;QAC1D,KAAK,aAAa;YAChB,OAAO,QAAQ,KAAK,OAAO,CAAC;QAC9B;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC"}