/**
 * Utilitaires pour Cloud Monitoring SIGMA
 * Envoi de métriques personnalisées et gestion des alertes
 */

import { logger } from "firebase-functions";

/**
 * Interface pour une métrique personnalisée
 */
interface CustomMetric {
  metricType: string;
  value: number;
  labels?: Record<string, string>;
  timestamp?: Date;
}

/**
 * Types de métriques SIGMA
 */
export const METRIC_TYPES = {
  CRITICAL_STOCK_COUNT: 'custom.googleapis.com/sigma/critical_stock_count',
  CRITICAL_OVERDUE_COUNT: 'custom.googleapis.com/sigma/critical_overdue_count',
  DASHBOARD_QUERY_DURATION: 'custom.googleapis.com/sigma/dashboard_query_duration',
  DASHBOARD_ERROR_RATE: 'custom.googleapis.com/sigma/dashboard_error_rate',
  FIRESTORE_READ_RATE: 'custom.googleapis.com/sigma/firestore_read_rate'
} as const;

/**
 * Client Cloud Monitoring (lazy loading)
 */
let monitoringClient: any = null;

/**
 * Initialiser le client Cloud Monitoring
 */
function getMonitoringClient() {
  if (!monitoringClient) {
    try {
      // Import dynamique pour éviter les erreurs en mode test
      const { MetricServiceClient } = require('@google-cloud/monitoring');
      monitoringClient = new MetricServiceClient();
    } catch (error) {
      logger.warn('Cloud Monitoring client non disponible', {
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }
  return monitoringClient;
}

/**
 * Envoyer une métrique personnalisée à Cloud Monitoring
 */
export async function sendCustomMetric(metric: CustomMetric): Promise<void> {
  // Bypass en mode test
  if (process.env.NODE_ENV === 'test') {
    logger.info('Métrique envoyée (mode test)', metric);
    return;
  }

  const client = getMonitoringClient();
  if (!client) {
    logger.warn('Client Cloud Monitoring non disponible, métrique ignorée', metric);
    return;
  }

  try {
    const projectId = process.env.GCLOUD_PROJECT || 'sigma-nova';
    const projectPath = client.projectPath(projectId);
    
    const dataPoint = {
      interval: {
        endTime: {
          seconds: Math.floor((metric.timestamp || new Date()).getTime() / 1000)
        }
      },
      value: {
        doubleValue: metric.value
      }
    };

    const timeSeries = {
      metric: {
        type: metric.metricType,
        labels: metric.labels || {}
      },
      resource: {
        type: 'cloud_function',
        labels: {
          function_name: process.env.FUNCTION_NAME || 'unknown',
          region: process.env.FUNCTION_REGION || 'europe-west1'
        }
      },
      points: [dataPoint]
    };

    await client.createTimeSeries({
      name: projectPath,
      timeSeries: [timeSeries]
    });

    logger.info('Métrique envoyée avec succès', {
      metricType: metric.metricType,
      value: metric.value,
      labels: metric.labels
    });

  } catch (error) {
    logger.error('Erreur lors de l\'envoi de la métrique', {
      metric,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}

/**
 * Envoyer le nombre d'alertes stock critiques
 */
export async function sendCriticalStockCount(count: number): Promise<void> {
  await sendCustomMetric({
    metricType: METRIC_TYPES.CRITICAL_STOCK_COUNT,
    value: count,
    labels: {
      severity: 'critical'
    }
  });
}

/**
 * Envoyer le nombre d'emprunts en retard critiques
 */
export async function sendCriticalOverdueCount(count: number): Promise<void> {
  await sendCustomMetric({
    metricType: METRIC_TYPES.CRITICAL_OVERDUE_COUNT,
    value: count,
    labels: {
      severity: 'critical'
    }
  });
}

/**
 * Envoyer la durée d'une requête dashboard
 */
export async function sendDashboardQueryDuration(
  functionName: string,
  durationMs: number
): Promise<void> {
  await sendCustomMetric({
    metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
    value: durationMs,
    labels: {
      function_name: functionName
    }
  });
}

/**
 * Envoyer le taux d'erreur dashboard
 */
export async function sendDashboardErrorRate(
  functionName: string,
  errorRate: number
): Promise<void> {
  await sendCustomMetric({
    metricType: METRIC_TYPES.DASHBOARD_ERROR_RATE,
    value: errorRate,
    labels: {
      function_name: functionName
    }
  });
}

/**
 * Envoyer le taux de lecture Firestore
 */
export async function sendFirestoreReadRate(readsPerSecond: number): Promise<void> {
  await sendCustomMetric({
    metricType: METRIC_TYPES.FIRESTORE_READ_RATE,
    value: readsPerSecond,
    labels: {
      database: 'default'
    }
  });
}

/**
 * Décorateur pour mesurer automatiquement la durée des fonctions
 */
export function measureDuration(functionName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;
        
        // Envoyer la métrique de durée
        await sendDashboardQueryDuration(functionName, duration);
        
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        
        // Envoyer la métrique de durée même en cas d'erreur
        await sendDashboardQueryDuration(functionName, duration);
        
        // Envoyer une métrique d'erreur
        await sendDashboardErrorRate(functionName, 1);
        
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Classe pour collecter et envoyer des métriques en batch
 */
export class MetricsBatch {
  private metrics: CustomMetric[] = [];
  private batchSize: number;
  private flushInterval: number;
  private timer: NodeJS.Timeout | null = null;

  constructor(batchSize = 10, flushIntervalMs = 30000) {
    this.batchSize = batchSize;
    this.flushInterval = flushIntervalMs;
    this.startAutoFlush();
  }

  /**
   * Ajouter une métrique au batch
   */
  add(metric: CustomMetric): void {
    this.metrics.push(metric);
    
    if (this.metrics.length >= this.batchSize) {
      this.flush();
    }
  }

  /**
   * Envoyer toutes les métriques en attente
   */
  async flush(): Promise<void> {
    if (this.metrics.length === 0) return;

    const metricsToSend = [...this.metrics];
    this.metrics = [];

    try {
      await Promise.all(
        metricsToSend.map(metric => sendCustomMetric(metric))
      );
      
      logger.info('Batch de métriques envoyé', {
        count: metricsToSend.length
      });
    } catch (error) {
      logger.error('Erreur lors de l\'envoi du batch de métriques', {
        count: metricsToSend.length,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Démarrer l'envoi automatique périodique
   */
  private startAutoFlush(): void {
    this.timer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  /**
   * Arrêter l'envoi automatique
   */
  stop(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    
    // Envoyer les métriques restantes
    this.flush();
  }
}

/**
 * Instance globale pour collecter les métriques
 */
export const globalMetricsBatch = new MetricsBatch();

/**
 * Middleware pour mesurer automatiquement les performances des Cloud Functions
 */
export function withMonitoring<T extends any[], R>(
  functionName: string,
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    let success = true;
    
    try {
      const result = await fn(...args);
      return result;
    } catch (error) {
      success = false;
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      
      // Envoyer les métriques
      globalMetricsBatch.add({
        metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: duration,
        labels: {
          function_name: functionName,
          success: success.toString()
        }
      });
      
      if (!success) {
        globalMetricsBatch.add({
          metricType: METRIC_TYPES.DASHBOARD_ERROR_RATE,
          value: 1,
          labels: {
            function_name: functionName
          }
        });
      }
    }
  };
}

/**
 * Utilitaire pour créer des alertes de test
 */
export async function sendTestMetrics(): Promise<void> {
  logger.info('Envoi de métriques de test pour validation des alertes');
  
  // Simuler des alertes stock critiques
  await sendCriticalStockCount(12);
  
  // Simuler des emprunts en retard
  await sendCriticalOverdueCount(6);
  
  // Simuler une latence élevée
  await sendDashboardQueryDuration('getDashboardData', 750);
  
  // Simuler un taux d'erreur élevé
  await sendDashboardErrorRate('getDashboardData', 0.08); // 8%
  
  logger.info('Métriques de test envoyées');
}
