---
type: always
title: "Règles Statutaires SIGMA"
description: "Règles fondamentales sur l'architecture, la sécurité, les conventions et les interdits du projet SIGMA. Doit être respecté en permanence."
---

# Règle SIGMA : Architecture, Sécurité & Conventions

## 1. Architecture & Communication Backend
- 1.1. 95% des interactions client->backend doivent utiliser une **Firebase Callable Function**.
- 1.2. Toute opération de modification de données critiques DOIT utiliser une **transaction Firestore** (`runTransaction` ou `writeBatch`).
- 1.3. Le temps réel client doit utiliser `onSnapshot` avec **pagination** (`startAfter`).

## 2. Sécurité & Authentification
- 2.1. La gestion des rôles se base sur les **Custom Claims** ('admin', 'regisseur', 'utilisateur').
- 2.2. Les règles de sécurité DOIVENT utiliser `request.auth.token.role`.
- 2.3. Les Cloud Functions DOIVENT valider le rôle de l'appelant.

## 3. Conventions de code & Arborescence
- 3.1. Respecter scrupuleusement l'arborescence de `docs/Architecture_SIGMA_v1.2.md`.
- 3.2. Utiliser la terminologie standardisée : "Emprunt", "Module", "Stock", "Livraison".
- 3.3. (Suggestion) Utiliser le format Conventional Commits pour les messages de commit.

## 4. Assurance Qualité & Tests
- 4.1. Toute nouvelle logique métier DOIT être accompagnée de **tests unitaires**.
- 4.2. Le code n'est considéré comme complet que si les tests **réussissent**.

## 5. Anti-Patterns & Interdits
- 5.1. **Ne jamais stocker de secrets** en clair dans le code.
- 5.2. **Ne pas introduire de dépendances npm** non prévues au plan.
- 5.3. **Ne pas utiliser de requêtes Firestore non limitées**.
- 5.4. **NE JAMAIS modifier les fichiers de configuration racine** (`.github/`, `.gitignore`, `firebase.json`, etc.). Ces fichiers sont sous gouvernance humaine.
- 5.5. Toute la logique backend doit être dans `./functions/src/`.
- 5.6. Toute la logique frontend doit être dans `./src/`.

## 6. Style & Format
- 6.1. Privilégier **TypeScript** pour les Cloud Functions.
- 6.2. Expliquer systématiquement le raisonnement derrière les choix de code complexes.
- 6.3. (Suggestion) Logger les erreurs avec `functions.logger.error()` et des détails pertinents.
EOF