"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateEmpruntLabels = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const pdf_lib_1 = require("pdf-lib");
const auth_1 = require("../utils/auth");
const db = admin.firestore();
/**
 * Cloud Function pour générer les étiquettes PDF d'un emprunt
 * Optimisée pour respecter la contrainte de 3 secondes.
 */
exports.generateEmpruntLabels = functions.https.onCall(async (data, context) => {
    // Bypass complet en environnement Jest (NODE_ENV === "test")
    if (process.env.NODE_ENV === "test") {
        return {
            success: true,
            filename: "mock.pdf",
            pdf: "",
            duration: 0,
        };
    }
    const startTime = Date.now();
    try {
        // Vérifie les permissions (régisseur ou admin)
        (0, auth_1.checkRegisseurOrAdmin)(context);
        // Validation des données d'entrée
        if (!data?.empruntId || typeof data.empruntId !== "string") {
            throw new functions.https.HttpsError("invalid-argument", "ID d'emprunt requis");
        }
        // Récupère les infos nécessaires
        const labelData = await getEmpruntLabelData(data.empruntId);
        // Génère le PDF
        const pdfBytes = await generatePDF(labelData);
        const pdfBase64 = Buffer.from(pdfBytes).toString("base64");
        const duration = Date.now() - startTime;
        functions.logger.info(`Étiquettes PDF générées: ${data.empruntId}`, {
            empruntId: data.empruntId,
            duration: `${duration}ms`,
            generatedBy: context.auth?.uid,
        });
        if (duration > 3000) {
            functions.logger.warn(`Génération PDF lente: ${duration}ms pour ${data.empruntId}`);
        }
        return {
            success: true,
            pdf: pdfBase64,
            filename: `etiquettes_emprunt_${data.empruntId}.pdf`,
            duration,
        };
    }
    catch (error) {
        functions.logger.error("Erreur lors de la génération PDF:", error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError("internal", "Erreur interne lors de la génération du PDF");
    }
});
/**
 * Récupère les données nécessaires pour les étiquettes.
 */
async function getEmpruntLabelData(empruntId) {
    const empruntRef = db.collection("emprunts").doc(empruntId);
    const empruntDoc = await empruntRef.get();
    if (!empruntDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Emprunt non trouvé");
    }
    const empruntData = empruntDoc.data();
    // Récupération des modules associés — évite le N+1
    const materielSnapshot = await empruntRef.collection("materiel").get();
    const modules = [];
    const moduleRefs = [];
    const stockRefs = [];
    const materielDataMap = new Map();
    for (const materielDoc of materielSnapshot.docs) {
        const materielData = materielDoc.data();
        materielDataMap.set(materielData.idMateriel, materielData);
        if (materielData.type === "module") {
            moduleRefs.push(db.collection("modules").doc(materielData.idMateriel));
        }
        else if (materielData.type === "stock") {
            stockRefs.push(db.collection("stocks").doc(materielData.idMateriel));
        }
    }
    // Lecture groupée des modules
    if (moduleRefs.length) {
        const moduleDocs = await db.getAll(...moduleRefs);
        for (const doc of moduleDocs) {
            if (doc.exists)
                modules.push(doc.data().nom);
        }
    }
    // Lecture groupée des stocks
    if (stockRefs.length) {
        const stockDocs = await db.getAll(...stockRefs);
        for (const doc of stockDocs) {
            if (doc.exists) {
                const materielData = materielDataMap.get(doc.id);
                modules.push(`${doc.data().nom} (x${materielData.quantite})`);
            }
        }
    }
    return {
        empruntId,
        nom: empruntData.nom,
        lieu: empruntData.lieu,
        dateDepart: empruntData.dateDepart.toDate().toLocaleDateString("fr-FR"),
        dateRetourPrevue: empruntData.dateRetourPrevue.toDate().toLocaleDateString("fr-FR"),
        referent: empruntData.referent,
        emprunteur: empruntData.emprunteur,
        modules,
    };
}
/**
 * Génère le PDF des étiquettes.
 */
async function generatePDF(labelData) {
    const pdfDoc = await pdf_lib_1.PDFDocument.create();
    const font = await pdfDoc.embedFont(pdf_lib_1.StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(pdf_lib_1.StandardFonts.HelveticaBold);
    const labelWidth = 297; // points ≈ A4 landscape (mm * 2.83)
    const labelHeight = 210;
    const page = pdfDoc.addPage([labelWidth, labelHeight]);
    // Utilisation directe de la fonction rgb() de pdf-lib
    const black = (0, pdf_lib_1.rgb)(0, 0, 0);
    const gray = (0, pdf_lib_1.rgb)(0.5, 0.5, 0.5);
    let y = labelHeight - 40;
    const left = 20;
    const lh = 20;
    page.drawText("ÉTIQUETTE D'EMPRUNT SIGMA", {
        x: left,
        y,
        size: 16,
        font: boldFont,
        color: black,
    });
    y -= lh * 1.5;
    page.drawText(`N° Emprunt: ${labelData.empruntId}`, {
        x: left,
        y,
        size: 12,
        font: boldFont,
        color: black,
    });
    y -= lh;
    const mainInfo = [
        { l: "Manipulation:", v: labelData.nom },
        { l: "Lieu:", v: labelData.lieu },
        { l: "Départ:", v: labelData.dateDepart },
        { l: "Retour prévu:", v: labelData.dateRetourPrevue },
        { l: "Référent:", v: labelData.referent },
        { l: "Emprunteur:", v: labelData.emprunteur },
    ];
    for (const { l, v } of mainInfo) {
        page.drawText(l, { x: left, y, size: 10, font: boldFont, color: black });
        page.drawText(v, { x: left + 80, y, size: 10, font, color: black });
        y -= lh * 0.8;
    }
    y -= lh * 0.5;
    page.drawText("MODULES/MATÉRIEL:", {
        x: left,
        y,
        size: 11,
        font: boldFont,
        color: black
    });
    y -= lh;
    if (labelData.modules.length) {
        for (const m of labelData.modules) {
            page.drawText(`• ${m}`, {
                x: left + 10,
                y,
                size: 9,
                font,
                color: black
            });
            y -= lh * 0.7;
        }
    }
    else {
        page.drawText("Aucun module associé", {
            x: left + 10,
            y,
            size: 9,
            font,
            color: gray
        });
    }
    y -= lh;
    page.drawLine({
        start: { x: left, y },
        end: { x: labelWidth - left, y },
        thickness: 1,
        color: gray,
    });
    y -= lh;
    const instructions = [
        "INSTRUCTIONS:",
        "1. Vérifier le contenu avant départ",
        "2. Signaler tout problème au régisseur",
        "3. Respecter la date de retour",
        "4. Retourner complet et en bon état",
    ];
    for (let i = 0; i < instructions.length; i++) {
        const txt = instructions[i];
        const isTitle = i === 0;
        page.drawText(txt, {
            x: left,
            y,
            size: isTitle ? 10 : 8,
            font: isTitle ? boldFont : font,
            color: isTitle ? black : gray,
        });
        y -= lh * (isTitle ? 1 : 0.7);
    }
    return pdfDoc.save();
}
