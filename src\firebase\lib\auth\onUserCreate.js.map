{"version": 3, "file": "onUserCreate.js", "sourceRoot": "", "sources": ["../../src/auth/onUserCreate.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,iEAAmD;AACnD,8CAA0D;AAC1D,wDAAwD;AACxD,kEAAoD;AAEpD;;;;;;;;;;;;GAYG;AACU,QAAA,YAAY,GAAG,SAAS;KAClC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAgB,EAAE,EAAE;;IAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAI,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACzC,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,MAAA,MAAA,IAAI,CAAC,YAAY,0CAAG,CAAC,CAAC,0CAAE,UAAU;SAC7C,CAAC,CAAC;QAEH,sDAAsD;QACtD,MAAM,IAAI,GAAG,IAAA,cAAO,GAAE,CAAC;QACvB,MAAM,WAAW,GAAG,aAAa,CAAC;QAClC,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,WAAW;YACjB,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACxC,cAAc,EAAE,QAAQ,EAAE,yCAAyC;YACnE,gBAAgB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SAC3C,CAAC;QAEF,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;QAEvD,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtC,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,IAAI,EAAE,WAAW;SAClB,CAAC,CAAC;QAEH,qDAAqD;QACrD,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;QAE1B,sDAAsD;QACtD,MAAM,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;;YAC5C,MAAM,OAAO,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAErD,+DAA+D;YAC/D,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACnD,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;oBACjD,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,KAAK;iBAClB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,gCAAgC;YAChC,MAAM,QAAQ,GAAG;gBACf,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAC7B,IAAI,EAAE,WAAW;gBAEjB,0BAA0B;gBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,QAAQ;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,QAAQ;gBAEnB,uBAAuB;gBACvB,WAAW,EAAE;oBACX;wBACE,YAAY,EAAE,IAAI;wBAClB,OAAO,EAAE,WAAW;wBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,SAAS,EAAE,QAAQ;wBACnB,cAAc,EAAE,QAAQ;wBACxB,MAAM,EAAE,gCAAgC;qBACzC;iBACF;gBAED,2BAA2B;gBAC3B,QAAQ,EAAE,CAAA,MAAA,MAAA,IAAI,CAAC,YAAY,0CAAG,CAAC,CAAC,0CAAE,UAAU,KAAI,SAAS;gBAEzD,mBAAmB;gBACnB,QAAQ,EAAE,IAAI;gBACd,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,KAAK;gBAE1C,yBAAyB;gBACzB,WAAW,EAAE;oBACX,QAAQ,EAAE,IAAI;oBACd,aAAa,EAAE;wBACb,KAAK,EAAE,IAAI;wBACX,OAAO,EAAE,IAAI;qBACd;oBACD,KAAK,EAAE,OAAO;iBACf;gBAED,mDAAmD;gBACnD,KAAK,EAAE;oBACL,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,IAAI;oBACjB,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,IAAI,IAAI,EAAE;iBAC3B;aACF,CAAC;YAEF,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEnC,MAAM,CAAC,IAAI,CAAC,4CAA4C,EAAE;gBACxD,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,WAAW;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC7C,MAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE;YAChE,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,WAAW;YACjB,eAAe,EAAE,aAAa;YAC9B,QAAQ,EAAE,MAAA,MAAA,IAAI,CAAC,YAAY,0CAAG,CAAC,CAAC,0CAAE,UAAU;SAC7C,CAAC,CAAC;QAEH,wEAAwE;QACxE,wDAAwD;IAE1D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE7C,MAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE;YACnE,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;YACvD,eAAe,EAAE,aAAa;SAC/B,CAAC,CAAC;QAEH,6EAA6E;QAC7E,oFAAoF;QAEpF,oDAAoD;QACpD,0DAA0D;IAC5D,CAAC;AACH,CAAC,CAAC,CAAC"}