import { setUserRole } from "../auth/setUserRole";
import * as admin from "firebase-admin";
import { adminAuth } from "./setupTests";

describe("Authentication Cloud Functions", () => {
  let testEnv: any;
  let wrapped: any;

  beforeAll(async () => {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    testEnv = require("firebase-functions-test")();
    
    // L'initialisation de Firebase Admin est maintenant gérée dans setupTests.ts
    
    // Créer un utilisateur de test avant les tests
    try {
      await adminAuth.createUser({
        uid: "test-user-uid",
        email: "<EMAIL>",
        password: "password"
      });
    } catch (error: unknown) {
      // L'utilisateur existe peut-être déjà, ignorer l'erreur
      const errorMessage = error instanceof Error ? error.message : "Erreur inconnue";
      console.log("Utilisateur test-user-uid existe déjà ou erreur de création:", errorMessage);
    }
    
    // Créer un admin pour les tests
    try {
      await adminAuth.createUser({
        uid: "test-admin-uid",
        email: "<EMAIL>",
        password: "password"
      });
    } catch (error: unknown) {
      // L'utilisateur existe peut-être déjà, ignorer l'erreur
      const errorMessage = error instanceof Error ? error.message : "Erreur inconnue";
      console.log("Utilisateur test-admin-uid existe déjà ou erreur de création:", errorMessage);
    }
    
    // Définir le rôle admin pour l'utilisateur admin
    try {
      await adminAuth.setCustomUserClaims("test-admin-uid", { role: "admin" });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Erreur inconnue";
      console.log("Erreur lors de la définition des claims admin:", errorMessage);
    }
    
    wrapped = testEnv.wrap(setUserRole);
  });

  afterAll(async () => {
    // Nettoyer les utilisateurs de test après les tests de manière sécurisée
    try {
      await adminAuth.deleteUser("test-user-uid");
    } catch (error: unknown) {
      // L'utilisateur n'existe peut-être pas, ignorer l'erreur
      const errorMessage = error instanceof Error ? error.message : "Erreur inconnue";
      console.log("Erreur lors de la suppression de test-user-uid:", errorMessage);
    }
    
    try {
      await adminAuth.deleteUser("test-admin-uid");
    } catch (error: unknown) {
      // L'utilisateur n'existe peut-être pas, ignorer l'erreur
      const errorMessage = error instanceof Error ? error.message : "Erreur inconnue";
      console.log("Erreur lors de la suppression de test-admin-uid:", errorMessage);
    }
    
    testEnv.cleanup();
  });

  test("devrait permettre à un admin d'assigner un rôle", async () => {
    const data = {
      userId: "test-user-uid",  // Utiliser l'UID directement
      role: "regisseur"
    };

    const context = {
      auth: {
        uid: "test-admin-uid",
        token: {
          email: "<EMAIL>",
          role: "admin"
        }
      }
    };

    // Appeler la fonction pour assigner le rôle
    const result = await wrapped(data, context);
    
    // Vérifier que la fonction s'est exécutée avec succès
    expect(result.success).toBe(true);
    expect(result.newRole).toBe("regisseur");
    
    // Dans l'environnement de test, nous ne pouvons pas facilement vérifier que les claims ont été appliqués
    // car l'émulateur ne met pas toujours à jour les claims immédiatement
    // Nous nous contentons donc de vérifier que la fonction a retourné le bon résultat
  });

  test("devrait retourner une erreur de permission si l'utilisateur n'est pas admin", async () => {
    const data = {
      userId: "test-user-uid",
      role: "regisseur"
    };

    const context = {
      auth: {
        uid: "user-uid",
        token: {
          email: "<EMAIL>",
          role: "utilisateur"  // Pas un admin
        }
      }
    };

    // Vérifier que l'erreur correspond au message exact de la fonction
    await expect(wrapped(data, context)).rejects.toThrow("Seul un admin peut modifier un rôle.");
  });
});
