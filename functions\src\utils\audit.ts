/**
 * Module d'audit et de logging pour SIGMA (Corrigé)
 */

import { getFirestore, FieldValue } from "firebase-admin/firestore";
import { logger } from "firebase-functions";
import { AuthenticatedUser } from "./auth";

export interface AuditLog {
  userId: string;
  userEmail: string; // Gardé en string pour la BDD
  userRole: string;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: Record<string, any>;
  timestamp: FirebaseFirestore.Timestamp;
  success: boolean;
  errorMessage?: string;
}

export async function logAuditEvent(
  user: AuthenticatedUser,
  action: string,
  options: {
    resource?: string;
    resourceId?: string;
    details?: Record<string, any>;
    success?: boolean;
    errorMessage?: string;
  } = {},
): Promise<void> {
  try {
    const db = getFirestore();
    const auditLog: AuditLog = {
      userId: user.uid,
      // CORRECTION: Gérer le cas où l'email est manquant
      userEmail: user.email || "unknown",
      userRole: user.role,
      action,
      resource: options.resource,
      resourceId: options.resourceId,
      details: options.details,
      timestamp: FieldValue.serverTimestamp() as FirebaseFirestore.Timestamp,
      success: options.success ?? true,
      errorMessage: options.errorMessage,
    };
    await db.collection("auditLogs").add(auditLog);
  } catch (error) {
    logger.error("Erreur lors de l'enregistrement du log d'audit", {
      userId: user.uid,
      action,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    });
  }
}
