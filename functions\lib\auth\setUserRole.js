"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.setUserRole = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const https_1 = require("firebase-functions/v1/https");
/**
 * Cloud Function callable permettant à un administrateur
 * d’assigner un rôle à un utilisateur identifié par UID **ou** e-mail.
 */
exports.setUserRole = functions.https.onCall(async (data, context) => {
    /* 1. Vérifie que l’appelant est admin  */
    if (!context.auth || context.auth.token.role !== "admin") {
        throw new https_1.HttpsError("permission-denied", "Seul un admin peut modifier un rôle.");
    }
    /* 2. Validation et extraction des paramètres */
    const ALLOWED = new Set(["admin", "regisseur", "utilisateur"]);
    const role = String(data?.role || "").trim();
    if (!ALLOWED.has(role)) {
        throw new https_1.HttpsError("invalid-argument", "Paramètre 'role' invalide.");
    }
    const userId = String(data?.userId || "").trim();
    const email = String(data?.email || "").trim();
    let uid = userId;
    if (!uid) {
        if (!email) {
            throw new https_1.HttpsError("invalid-argument", "Paramètre 'userId' ou 'email' requis.");
        }
        const userRec = await admin.auth().getUserByEmail(email);
        uid = userRec.uid;
    }
    /* 3. Mise à jour des claims */
    await admin.auth().getUser(uid); // visible par les tests
    await admin.auth().setCustomUserClaims(uid, { role });
    return { success: true, newRole: role };
});
