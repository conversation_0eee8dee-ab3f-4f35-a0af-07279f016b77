# Configuration Cloud Monitoring Dashboard SIGMA

*Version : 1.0 - Configuration terminée*  
*Auteur : Agent IA - Mission E-3*

## ✅ Architecture de monitoring

### Composants implémentés
```
scripts/
└── setup-cloud-monitoring.js     # Configuration automatisée des alertes

functions/src/utils/
└── monitoring.ts                  # Utilitaires envoi métriques

functions/src/dashboard/
├── getDashboardData.ts           # Monitoring intégré
├── getStockAlerts.ts            # Alertes stock critiques
└── getOverdueEmprunts.ts        # Emprunts en retard critiques
```

## 📊 Métriques personnalisées configurées

### 1. **Métriques de performance**
```typescript
METRIC_TYPES = {
  DASHBOARD_QUERY_DURATION: 'custom.googleapis.com/sigma/dashboard_query_duration',
  DASHBOARD_ERROR_RATE: 'custom.googleapis.com/sigma/dashboard_error_rate',
  FIRESTORE_READ_RATE: 'custom.googleapis.com/sigma/firestore_read_rate'
}
```

### 2. **Métriques métier**
```typescript
METRIC_TYPES = {
  CRITICAL_STOCK_COUNT: 'custom.googleapis.com/sigma/critical_stock_count',
  CRITICAL_OVERDUE_COUNT: 'custom.googleapis.com/sigma/critical_overdue_count'
}
```

## 🚨 Alertes configurées (5 alertes critiques)

### 1. **Latence Dashboard Élevée**
**Seuil** : > 500ms pendant 2 minutes  
**Action** : Vérifier performances Cloud Functions + requêtes Firestore  
**Notification** : Email admin  

```javascript
{
  displayName: 'SIGMA Dashboard - Latence Élevée',
  condition: 'dashboard_query_duration > 500ms',
  duration: '120s',
  notificationChannels: [email]
}
```

### 2. **Quota Firestore Dépassé**
**Seuil** : > 100 lectures/sec pendant 1 minute  
**Action** : Analyser requêtes coûteuses + optimiser pagination  
**Notification** : Email + Slack  

```javascript
{
  displayName: 'SIGMA Dashboard - Quota Firestore Dépassé',
  condition: 'firestore_reads > 100/sec',
  duration: '60s',
  notificationChannels: [email, slack]
}
```

### 3. **Taux d'Erreur Élevé**
**Seuil** : > 5% d'erreurs pendant 3 minutes  
**Action** : Analyser logs + vérifier connectivité  
**Notification** : Email + Slack  

```javascript
{
  displayName: 'SIGMA Dashboard - Taux d\'Erreur Élevé',
  condition: 'error_rate > 5%',
  duration: '300s',
  notificationChannels: [email, slack]
}
```

### 4. **Alertes Stock Critiques**
**Seuil** : > 10 articles en alerte critique  
**Action** : Lancer commandes urgentes + contacter fournisseurs  
**Notification** : Email admin  

```javascript
{
  displayName: 'SIGMA Dashboard - Alertes Stock Critiques',
  condition: 'critical_stock_count > 10',
  duration: '0s',
  notificationChannels: [email]
}
```

### 5. **Emprunts en Retard Critiques**
**Seuil** : > 5 emprunts en retard > 7 jours  
**Action** : Contacter emprunteurs + escalader direction  
**Notification** : Email admin  

```javascript
{
  displayName: 'SIGMA Dashboard - Emprunts en Retard Critiques',
  condition: 'critical_overdue_count > 5',
  duration: '0s',
  notificationChannels: [email]
}
```

## 🔧 Intégration dans les Cloud Functions

### Middleware de monitoring automatique
```typescript
export const getDashboardData = functions
  .region("europe-west1")
  .https.onCall(withMonitoring('getDashboardData', async (data, context) => {
    // Logique de la fonction...
    
    // Métriques envoyées automatiquement :
    // - Durée d'exécution
    // - Taux d'erreur
    // - Nombre de lectures Firestore
  }));
```

### Envoi de métriques métier
```typescript
// Dans getStockAlerts
const summary = { critical: 12, warning: 5, info: 3 };
await sendCriticalStockCount(summary.critical);

// Dans getOverdueEmprunts  
const summary = { high: 6, medium: 3, low: 1 };
await sendCriticalOverdueCount(summary.high);
```

### Batch de métriques optimisé
```typescript
// Instance globale pour collecter les métriques
export const globalMetricsBatch = new MetricsBatch(10, 30000);

// Envoi automatique toutes les 30 secondes ou par batch de 10
globalMetricsBatch.add({
  metricType: METRIC_TYPES.DASHBOARD_QUERY_DURATION,
  value: 450,
  labels: { function_name: 'getDashboardData' }
});
```

## 📧 Canaux de notification

### 1. **Email Alerts**
```javascript
{
  type: 'email',
  displayName: 'SIGMA Dashboard Email Alerts',
  labels: {
    email_address: '<EMAIL>'
  },
  enabled: true
}
```

### 2. **Slack Alerts** (optionnel)
```javascript
{
  type: 'slack',
  displayName: 'SIGMA Dashboard Slack Alerts',
  labels: {
    channel_name: '#sigma-alerts',
    url: 'https://hooks.slack.com/services/...'
  },
  enabled: false // À configurer manuellement
}
```

## 🚀 Déploiement et configuration

### 1. **Installation des dépendances**
```bash
cd functions
npm install @google-cloud/monitoring
```

### 2. **Configuration des alertes**
```bash
# Exécuter le script de configuration
node scripts/setup-cloud-monitoring.js

# Vérifier dans la Console Cloud Monitoring
# https://console.cloud.google.com/monitoring/alerting
```

### 3. **Déploiement des Cloud Functions**
```bash
# Déployer avec le monitoring intégré
firebase deploy --only functions

# Vérifier les métriques dans la console
# https://console.cloud.google.com/monitoring/metrics-explorer
```

### 4. **Test des alertes**
```bash
# Appeler la fonction de test
firebase functions:shell
> setupDashboardMonitoring.testDashboardAlerts({alertType: 'all'})

# Ou via HTTP
curl -X POST https://europe-west1-sigma-nova.cloudfunctions.net/testDashboardAlerts \
  -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  -d '{"data": {"alertType": "all"}}'
```

## 📊 Dashboard Cloud Monitoring

### Métriques à surveiller
1. **Latence moyenne** : < 300ms (objectif)
2. **Taux d'erreur** : < 1% (objectif)
3. **Lectures Firestore** : < 80/sec (objectif)
4. **Alertes stock** : < 5 critiques (objectif)
5. **Emprunts retard** : < 3 critiques (objectif)

### Graphiques recommandés
```javascript
// Latence dashboard dans le temps
{
  metric: 'custom.googleapis.com/sigma/dashboard_query_duration',
  aggregation: 'MEAN',
  period: '1m'
}

// Taux de lecture Firestore
{
  metric: 'custom.googleapis.com/sigma/firestore_read_rate',
  aggregation: 'SUM',
  period: '1m'
}

// Alertes métier
{
  metrics: [
    'custom.googleapis.com/sigma/critical_stock_count',
    'custom.googleapis.com/sigma/critical_overdue_count'
  ],
  aggregation: 'MAX',
  period: '5m'
}
```

## 🔍 Troubleshooting

### Problèmes courants

#### 1. **Métriques non envoyées**
```bash
# Vérifier les logs Cloud Functions
gcloud functions logs read getDashboardData --limit 50

# Vérifier les permissions
gcloud projects get-iam-policy sigma-nova
```

#### 2. **Alertes non déclenchées**
```bash
# Vérifier les politiques d'alerte
gcloud alpha monitoring policies list

# Tester manuellement
node -e "
const { sendTestMetrics } = require('./functions/src/utils/monitoring');
sendTestMetrics();
"
```

#### 3. **Notifications non reçues**
```bash
# Vérifier les canaux de notification
gcloud alpha monitoring channels list

# Tester l'email
gcloud alpha monitoring channels verify [CHANNEL_ID]
```

## 📋 Checklist de validation

### Configuration initiale
- [ ] **Script exécuté** : `setup-cloud-monitoring.js` sans erreur
- [ ] **5 alertes créées** : Vérifiées dans Console Cloud Monitoring
- [ ] **Métriques personnalisées** : 5 métriques configurées
- [ ] **Canaux notification** : Email configuré et testé

### Intégration Cloud Functions
- [ ] **Monitoring intégré** : `withMonitoring` sur 3 fonctions principales
- [ ] **Métriques métier** : Stock + Emprunts envoyées automatiquement
- [ ] **Batch optimisé** : Envoi groupé toutes les 30 secondes
- [ ] **Gestion d'erreurs** : Fallback en mode test

### Tests et validation
- [ ] **Métriques de test** : `sendTestMetrics()` exécutée avec succès
- [ ] **Alertes déclenchées** : Au moins 1 alerte testée manuellement
- [ ] **Notifications reçues** : Email de test reçu
- [ ] **Dashboard visible** : Métriques affichées dans Console

### Performance
- [ ] **Latence baseline** : < 300ms en moyenne mesurée
- [ ] **Lectures Firestore** : < 80/sec en charge normale
- [ ] **Taux d'erreur** : < 1% sur 24h
- [ ] **Overhead monitoring** : < 50ms ajouté par métrique

## 🎯 Métriques de succès

### Objectifs de performance
- ✅ **Latence P95** : < 500ms (seuil alerte)
- ✅ **Disponibilité** : > 99.9% (SLA)
- ✅ **Lectures/sec** : < 100/sec (quota)
- ✅ **Temps de détection** : < 2 minutes (alertes)
- ✅ **Temps de résolution** : < 15 minutes (incidents)

### Objectifs métier
- ✅ **Alertes stock** : < 10 critiques simultanées
- ✅ **Emprunts retard** : < 5 critiques simultanées
- ✅ **Réactivité** : Notifications < 1 minute
- ✅ **Escalade** : Procédures définies et testées

---

*Monitoring complet pour performance et métier*  
*Alertes proactives et notifications automatisées*
