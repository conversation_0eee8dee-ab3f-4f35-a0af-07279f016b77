@echo off
echo 🧪 Démarrage des tests SIGMA avec émulateurs
echo ============================================

REM Vérifier si nous sommes dans le bon répertoire
if not exist "firebase.json" (
    echo ❌ Ce script doit être exécuté depuis la racine du projet SIGMA
    exit /b 1
)

REM Démarrer les émulateurs en arrière-plan
echo 🔧 Démarrage des émulateurs Firebase...
start /B firebase emulators:start --only auth,firestore,functions

REM Attendre que les émulateurs se lancent
echo ⏳ Attente du démarrage des émulateurs (15 secondes)...
timeout /t 15 /nobreak > nul

REM Lancer les tests
echo 🧪 Lancement des tests...
cd src\firebase\functions
npm test

REM Arrêter les émulateurs
echo 🛑 Arrêt des émulateurs...
taskkill /f /im node.exe /fi "WINDOWTITLE eq Firebase Emulators*" > nul 2>&1

echo ✅ Tests terminés
pause
