"use strict";
/**
 * Utilitaires pour Cloud Monitoring SIGMA
 * Envoi de métriques personnalisées et gestion des alertes
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalMetricsBatch = exports.MetricsBatch = exports.METRIC_TYPES = void 0;
exports.sendCustomMetric = sendCustomMetric;
exports.sendCriticalStockCount = sendCriticalStockCount;
exports.sendCriticalOverdueCount = sendCriticalOverdueCount;
exports.sendDashboardQueryDuration = sendDashboardQueryDuration;
exports.sendDashboardErrorRate = sendDashboardErrorRate;
exports.sendFirestoreReadRate = sendFirestoreReadRate;
exports.measureDuration = measureDuration;
exports.withMonitoring = withMonitoring;
exports.sendTestMetrics = sendTestMetrics;
const firebase_functions_1 = require("firebase-functions");
/**
 * Types de métriques SIGMA
 */
exports.METRIC_TYPES = {
    CRITICAL_STOCK_COUNT: 'custom.googleapis.com/sigma/critical_stock_count',
    CRITICAL_OVERDUE_COUNT: 'custom.googleapis.com/sigma/critical_overdue_count',
    DASHBOARD_QUERY_DURATION: 'custom.googleapis.com/sigma/dashboard_query_duration',
    DASHBOARD_ERROR_RATE: 'custom.googleapis.com/sigma/dashboard_error_rate',
    FIRESTORE_READ_RATE: 'custom.googleapis.com/sigma/firestore_read_rate'
};
/**
 * Client Cloud Monitoring (lazy loading)
 */
let monitoringClient = null;
/**
 * Initialiser le client Cloud Monitoring
 */
function getMonitoringClient() {
    if (!monitoringClient) {
        try {
            // Import dynamique pour éviter les erreurs en mode test
            const { MetricServiceClient } = require('@google-cloud/monitoring');
            monitoringClient = new MetricServiceClient();
        }
        catch (error) {
            firebase_functions_1.logger.warn('Cloud Monitoring client non disponible', {
                error: error instanceof Error ? error.message : String(error)
            });
            return null;
        }
    }
    return monitoringClient;
}
/**
 * Envoyer une métrique personnalisée à Cloud Monitoring
 */
async function sendCustomMetric(metric) {
    // Bypass en mode test
    if (process.env.NODE_ENV === 'test') {
        firebase_functions_1.logger.info('Métrique envoyée (mode test)', metric);
        return;
    }
    const client = getMonitoringClient();
    if (!client) {
        firebase_functions_1.logger.warn('Client Cloud Monitoring non disponible, métrique ignorée', metric);
        return;
    }
    try {
        const projectId = process.env.GCLOUD_PROJECT || 'sigma-nova';
        const projectPath = client.projectPath(projectId);
        const dataPoint = {
            interval: {
                endTime: {
                    seconds: Math.floor((metric.timestamp || new Date()).getTime() / 1000)
                }
            },
            value: {
                doubleValue: metric.value
            }
        };
        const timeSeries = {
            metric: {
                type: metric.metricType,
                labels: metric.labels || {}
            },
            resource: {
                type: 'cloud_function',
                labels: {
                    function_name: process.env.FUNCTION_NAME || 'unknown',
                    region: process.env.FUNCTION_REGION || 'europe-west1'
                }
            },
            points: [dataPoint]
        };
        await client.createTimeSeries({
            name: projectPath,
            timeSeries: [timeSeries]
        });
        firebase_functions_1.logger.info('Métrique envoyée avec succès', {
            metricType: metric.metricType,
            value: metric.value,
            labels: metric.labels
        });
    }
    catch (error) {
        firebase_functions_1.logger.error('Erreur lors de l\'envoi de la métrique', {
            metric,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
        });
    }
}
/**
 * Envoyer le nombre d'alertes stock critiques
 */
async function sendCriticalStockCount(count) {
    await sendCustomMetric({
        metricType: exports.METRIC_TYPES.CRITICAL_STOCK_COUNT,
        value: count,
        labels: {
            severity: 'critical'
        }
    });
}
/**
 * Envoyer le nombre d'emprunts en retard critiques
 */
async function sendCriticalOverdueCount(count) {
    await sendCustomMetric({
        metricType: exports.METRIC_TYPES.CRITICAL_OVERDUE_COUNT,
        value: count,
        labels: {
            severity: 'critical'
        }
    });
}
/**
 * Envoyer la durée d'une requête dashboard
 */
async function sendDashboardQueryDuration(functionName, durationMs) {
    await sendCustomMetric({
        metricType: exports.METRIC_TYPES.DASHBOARD_QUERY_DURATION,
        value: durationMs,
        labels: {
            function_name: functionName
        }
    });
}
/**
 * Envoyer le taux d'erreur dashboard
 */
async function sendDashboardErrorRate(functionName, errorRate) {
    await sendCustomMetric({
        metricType: exports.METRIC_TYPES.DASHBOARD_ERROR_RATE,
        value: errorRate,
        labels: {
            function_name: functionName
        }
    });
}
/**
 * Envoyer le taux de lecture Firestore
 */
async function sendFirestoreReadRate(readsPerSecond) {
    await sendCustomMetric({
        metricType: exports.METRIC_TYPES.FIRESTORE_READ_RATE,
        value: readsPerSecond,
        labels: {
            database: 'default'
        }
    });
}
/**
 * Décorateur pour mesurer automatiquement la durée des fonctions
 */
function measureDuration(functionName) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        descriptor.value = async function (...args) {
            const startTime = Date.now();
            try {
                const result = await method.apply(this, args);
                const duration = Date.now() - startTime;
                // Envoyer la métrique de durée
                await sendDashboardQueryDuration(functionName, duration);
                return result;
            }
            catch (error) {
                const duration = Date.now() - startTime;
                // Envoyer la métrique de durée même en cas d'erreur
                await sendDashboardQueryDuration(functionName, duration);
                // Envoyer une métrique d'erreur
                await sendDashboardErrorRate(functionName, 1);
                throw error;
            }
        };
        return descriptor;
    };
}
/**
 * Classe pour collecter et envoyer des métriques en batch
 */
class MetricsBatch {
    constructor(batchSize = 10, flushIntervalMs = 30000) {
        this.metrics = [];
        this.timer = null;
        this.batchSize = batchSize;
        this.flushInterval = flushIntervalMs;
        this.startAutoFlush();
    }
    /**
     * Ajouter une métrique au batch
     */
    add(metric) {
        this.metrics.push(metric);
        if (this.metrics.length >= this.batchSize) {
            this.flush();
        }
    }
    /**
     * Envoyer toutes les métriques en attente
     */
    async flush() {
        if (this.metrics.length === 0)
            return;
        const metricsToSend = [...this.metrics];
        this.metrics = [];
        try {
            await Promise.all(metricsToSend.map(metric => sendCustomMetric(metric)));
            firebase_functions_1.logger.info('Batch de métriques envoyé', {
                count: metricsToSend.length
            });
        }
        catch (error) {
            firebase_functions_1.logger.error('Erreur lors de l\'envoi du batch de métriques', {
                count: metricsToSend.length,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
    /**
     * Démarrer l'envoi automatique périodique
     */
    startAutoFlush() {
        this.timer = setInterval(() => {
            this.flush();
        }, this.flushInterval);
    }
    /**
     * Arrêter l'envoi automatique
     */
    stop() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        // Envoyer les métriques restantes
        this.flush();
    }
}
exports.MetricsBatch = MetricsBatch;
/**
 * Instance globale pour collecter les métriques
 */
exports.globalMetricsBatch = new MetricsBatch();
/**
 * Middleware pour mesurer automatiquement les performances des Cloud Functions
 */
function withMonitoring(functionName, fn) {
    return async (...args) => {
        const startTime = Date.now();
        let success = true;
        try {
            const result = await fn(...args);
            return result;
        }
        catch (error) {
            success = false;
            throw error;
        }
        finally {
            const duration = Date.now() - startTime;
            // Envoyer les métriques
            exports.globalMetricsBatch.add({
                metricType: exports.METRIC_TYPES.DASHBOARD_QUERY_DURATION,
                value: duration,
                labels: {
                    function_name: functionName,
                    success: success.toString()
                }
            });
            if (!success) {
                exports.globalMetricsBatch.add({
                    metricType: exports.METRIC_TYPES.DASHBOARD_ERROR_RATE,
                    value: 1,
                    labels: {
                        function_name: functionName
                    }
                });
            }
        }
    };
}
/**
 * Utilitaire pour créer des alertes de test
 */
async function sendTestMetrics() {
    firebase_functions_1.logger.info('Envoi de métriques de test pour validation des alertes');
    // Simuler des alertes stock critiques
    await sendCriticalStockCount(12);
    // Simuler des emprunts en retard
    await sendCriticalOverdueCount(6);
    // Simuler une latence élevée
    await sendDashboardQueryDuration('getDashboardData', 750);
    // Simuler un taux d'erreur élevé
    await sendDashboardErrorRate('getDashboardData', 0.08); // 8%
    firebase_functions_1.logger.info('Métriques de test envoyées');
}
