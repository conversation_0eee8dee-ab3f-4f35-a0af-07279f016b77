
# Architecture Globale – **SIGMA**
*Version : 1.2 – mise à jour du 09 July 2025*  
*Auteur : Aymeric*

> **Objet**  
> Fournir un référentiel d’architecture précis et directement exploitable par les développeurs **et** par les agents IA (Augment Code, Copilot, etc.).  
> Ce document doit rester la **source de vérité** pour la configuration, la sécurité et la structure du projet.

---

## 1 · Vue d’ensemble

| Élément | Détail |
|---------|--------|
| **Nom du projet** | SIGMA |
| **Type d’appli** | Outil interne de gestion logistique |
| **Utilisateurs** | ≈ 30 collaborateurs |
| **Objectif métier** | Suivi centralisé des **emprunts / stocks / modules / livraisons** afin d’optimiser les coûts et la traçabilité |
| **Référence tâches** | [`@sigma_tasks.yaml`](../SIGMA_tasks.yaml) |

---

## 2 · Périmètre technique & choix clés

### 2.1 Communication Client → Backend

| Canal | Usage recommandé | Notes |
|-------|------------------|-------|
| **Firebase Callable Functions** | 95 % des appels métier | Auth Firebase intégrée, testable via Emulators |
| `google.script.run` (GAS) | **Exceptionnel** – appels ciblés à Google Workspace | Surveiller quotas GAS ; préférer CF dès que possible |

### 2.2 Backend

| Couche | Technologie | Rôle principal |
|--------|-------------|----------------|
| **Données** | **Firestore** (mode prod) | Persistance NoSQL, temps réel, transactions critiques |
| **Logique serveur** | **Cloud Functions (Node 18, TS)** | CRUD avancé, validations, tâches planifiées, PDF |
| **Interface minimale** | **Google Apps Script** | Sert l’UI HTML/JS, wrappers, orchestration simple |

> **Transaction & cohérence** : toujours utiliser `runTransaction` ou `writeBatch` dans les CF pour les opérations sensibles (emprunts, stocks).  
> **Temps réel** : `onSnapshot` côté client avec pagination (`startAfter`) pour éviter la surlecture.

### 2.3 Authentification & rôles

- **Service** : Firebase Auth (Google Sign‑In).  
- **Gestion des rôles** : **Custom Claims** via CF `setUserRole` (Option B).  
- **Rôles** : `admin`, `regisseur`, `utilisateur` — voir `docs/Permissions.md`.  
- **Règles** : Firestore & Storage basées sur `request.auth.token.role`.

### 2.4 Sécurité & performance

- Règles robustes (`firestore.rules`, `storage.rules`) + tests Emulator Suite.  
- Indexes composites (`firestore.indexes.json`).  
- Cold‑start < 400 ms via groupement des CF, `minInstances` sélectifs.  
- Monitoring coûts/latence dans Cloud Monitoring.

### 2.5 Observabilité & maintenance

| Sujet | Implémentation |
|-------|----------------|
| **Logs** | `functions.logger`, console GAS → Cloud Logging |
| **Monitoring** | Dashboards Cloud Monitoring + alertes latence 500 ms |
| **Sauvegarde** | CF `backupFirestoreToGCS` + restauration mensuelle |
| **Maintenance** | Flag Firestore `config/maintenance` contrôlé côté client/serveur |

### 2.6 Déploiement & CI/CD

- **Apps Script** : CLASP (`clasp push`).  
- **Firebase** : `firebase deploy`.  
- **CI/CD** : GitHub Actions → lint ▶ tests ▶ deploy (dev).  

---

## 3 · Arborescence de référence

./
├── .github/
├── cypress/
├── docs/
├── functions/      # Code des Cloud Functions (backend)
│   ├── src/
│   ├── package.json
│   └── ...
├── scripts/
├── src/            # Code Google Apps Script (frontend/glue code)
│   ├── html/
│   ├── js/
│   └── ...
├── firebase.json
├── firestore.rules
└── ...

---

## 4 · Conventions « Agent IA »

### 4.1 Format d’instruction

```
CONTEXT   : @docs/Architecture.md, @sigma_tasks.yaml
OBJECTIVE : …
ACTION    :
  1. …
TARGET    : [ "path/to/file" ]
EXPECTED  : tests verts, couverture ≥80 %
TIP       : …
```

### 4.2 Terminologie standard

- **Emprunt** : document `emprunts/<built-in function id>`.  
- **Module** : document `modules/<built-in function id>`.  
- **Stock** : document `stocks/<built-in function id>`.  
- **Livraison** : document `livraisons/<built-in function id>`.  

---

## 5 · Roadmap d’évolution

| Axe | Prochaines étapes |
|-----|-------------------|
| Data contracts | Générer validators (`zod`) depuis `docs/schemas` |
| Observabilité | Tracer coût Firestore par fonction |
| CI → CD | Déploiement Apps Script via GitHub OIDC |
| Node 20 | Migration CF avant fin 2025 |

---

### Historique

| Version | Date | Notes |
|---------|------|-------|
| 1.0 | 05‑06‑2025 | Première rédaction |
| 1.1 | 20‑06‑2025 | Ajustements rôles & CF |
| 1.2 | 09 July 2025 | IA‑friendly, prompts & arborescence clarifiées |

---

_Fin du document_
