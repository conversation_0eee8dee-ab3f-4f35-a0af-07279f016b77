

---

# Plan Directeur des Épiques SIGMA (v2.1)

Ce document sert de plan de mission central pour le développement de SIGMA. Chaque ligne représente une mission autonome, liant l'objectif stratégique aux documents techniques et au processus de développement attendu.

---

## Épique E-1 : Sécurité & Authentification

* **Objectif global** : Mettre en place une authentification Google et une gestion fine des rôles pour protéger l'accès.
* **Documents de référence clés** :

  * @docs/Permissions.md
  * @docs/Architecture\_SIGMA\_v1.2.md
* **Dépendances notables** : firebase-admin
* **Critères de succès** :

  * Une Pull Request est ouverte contenant l'implémentation de Google OAuth.
  * Custom Claims (admin, régisseur, utilisateur) injectés via Cloud Function.
  * Règles Firestore/Storage basées sur les rôles.
  * Tests d'accès non autorisé inclus dans la PR sont 100 % KO.
* **Pilote** : Agent

---

## Épique E-2 : Flux « Emprunts »

* **Objectif global** : Industrialiser le cycle complet des emprunts : création, départ, retour, et génération de PDF.
* **Documents de référence clés** :
  * @docs/Gestion des Emprunts dans SIGMA Firebase.md
  * @docs/Interface_SIGMA_v1.0.md
  * @docs/DataModel.md
* **Dépendances notables** : pdf-lib, **playwright**
* **Critères de succès** :
  * Une PR est ouverte contenant les 3 Cloud Functions (création, workflow) et leurs tests (couverture ≥90 %).
  * Génération PDF des étiquettes en moins de 3 secondes.
  * **Tests Playwright** du formulaire multi-étapes passent avec succès.
* **Pilote** : Agent

---

## Épique E-3 : Dashboard & Alerting

* **Objectif global** : Offrir un dashboard temps-réel performant avec des alertes automatiques.
* **Documents de référence clés** :
  * @docs/Onglet Résumé de SIGMA Firebase.md
  * @docs/Interface_SIGMA_v1.0.md
* **Dépendances notables** : lighthouse, **playwright**
* **Critères de succès** :
  * Une PR est ouverte avec le code du dashboard.
  * Listeners temps-réel (stocks, retards) fonctionnels.
  * Requêtes Firestore optimisées (<100 lectures/sec).
  * Alertes Cloud Monitoring actives.
  * Score Lighthouse performance ≥ 90.
  * **Tests Playwright des scénarios clés (navigation, filtres) passent avec succès.**
* **Pilote** : Agent

---

## Épique E-4 : Expérience Stocks & Livraisons

* **Objectif global** : Améliorer la visualisation logistique avec des filtres avancés et une carte interactive.
* **Documents de référence clés** :

  * @docs/État des Stocks dans SIGMA.md
  * @docs/Interface\_SIGMA\_v1.0.md
  * @docs/Gestion des Modules dans SIGMA.md
* **Dépendances notables** : leaflet
* **Critères de succès** :

  * Une PR est ouverte incluant l'écran Stocks avec filtres dynamiques (<200ms) et la carte Leaflet fonctionnelle.
  * Tests UX validés par les régisseurs.
* **Pilote** : Agent

---

## Épique E-5 : Observabilité & Performance

* **Objectif global** : Instrumenter SIGMA pour détecter les régressions de performance et les coûts anormaux.
* **Documents de référence clés** :

  * @docs/Architecture\_SIGMA\_v1.2.md
* **Dépendances notables** : (aucune)
* **Critères de succès** :

  * Une PR est ouverte avec la configuration (config-as-code) du dashboard Cloud Monitoring (CPU, Firestore, coûts).
  * Alertes sur latence et quotas actives.
  * Cold-start moyen des CF < 400ms.
* **Pilote** : Agent

---

## Épique E-6 : Back-ups & Continuité

* **Objectif global** : Garantir la restauration des données et la continuité de service.
* **Documents de référence clés** :

  * @docs/Architecture\_SIGMA\_v1.2.md
* **Dépendances notables** : @google-cloud/firestore
* **Critères de succès** :

  * Une PR est ouverte avec le script de restauration GCS → Firestore.
  * Le script est testé mensuellement en staging par le pilote humain.
  * Smoke-tests 100 % verts après restauration.
* **Pilote** : Agent

---

## Épique E-7 : Qualité & Sécurité

* **Objectif global** : Renforcer la base de code contre les vulnérabilités et garantir la conformité.
* **Documents de référence clés** :

  * @docs/Permissions.md
  * @docs/Architecture\_SIGMA\_v1.2.md
* **Dépendances notables** : eslint-plugin-security
* **Critères de succès** :

  * Une PR est ouverte adressant les points critiques identifiés par un audit de sécurité (type OWASP).
  * Règles de Storage revues.
  * Accessibilité WCAG AA validée.
* **Pilote** : Agent

---

## Épique E-8 : Pré-production & Release

* **Objectif global** : Verrouiller le périmètre fonctionnel, tester le rollback et préparer la v1.0.
* **Documents de référence clés** :

  * @docs/Plan de Développement Détaillé - Application SIGMA.md
* **Dépendances notables** : (aucune)
* **Critères de succès** :

  * Checklist pré-production signée.
  * Une PR est ouverte avec la procédure de rollback automatisée et testée.
  * Tag v1.0.0 créé sur la branche main.
* **Pilote** : Agent & Humain

---

## Épique E-9 : Gouvernance des Données

* **Objectif global** : Stabiliser le modèle de données et la documentation technique.
* **Documents de référence clés** :

  * @docs/DataModel.md
* **Dépendances notables** : zod
* **Critères de succès** :

  * Une PR est ouverte intégrant la génération automatique des schémas (Zod) à partir du DataModel.
  * DataModel.md est gelé en v0.9 et validé.
  * Taux de complétude de la documentation > 95 %.
* **Pilote** : Agent

---

## Épique E-10 : Vision Produit & Feedback

* **Objectif global** : Aligner la roadmap sur les retours utilisateurs pour les versions futures.
* **Documents de référence clés** : N/A (Tâche de Product Management)
* **Dépendances notables** : N/A
* **Critères de succès** :

  * Atelier story-mapping terminé.
  * Retours utilisateurs consolidés.
  * Roadmap v1.1 publiée et validée.
* **Pilote** : Humain

---

