"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
const admin = __importStar(require("firebase-admin"));
/**
 * 🚀 Initialisation du SDK Firebase Admin.
 *
 * On vérifie si une instance de l'application Firebase (admin) a déjà été initialisée.
 * Si ce n'est pas le cas (longueur du tableau `admin.apps` est 0), on l'initialise.
 *
 * Cette approche, connue sous le nom de "lazy initialization", est cruciale pour
 * éviter les timeouts au démarrage de l'émulateur ou lors du déploiement.
 * Elle garantit que l'initialisation ne se produit qu'une seule fois et uniquement
 * lorsque c'est nécessaire, sans bloquer le processus de découverte des fonctions.
 */
if (!admin.apps.length) {
    admin.initializeApp();
}
// ======================================================================================
// 🔥 EXPORTATION DES CLOUD FUNCTIONS
//
// Chaque fichier exporté ci-dessous contient la logique pour une ou plusieurs
// Cloud Functions. Le fait de les regrouper par fonctionnalité (auth, etc.)
// permet de garder le code organisé et maintenable.
// ======================================================================================
// --- Fonctions liées à l'authentification et à la gestion des utilisateurs ---
__exportStar(require("./auth/onUserCreate"), exports);
__exportStar(require("./auth/setUserRole"), exports);
__exportStar(require("./auth/userManagement"), exports); // Assurez-vous que le nom du fichier correspond
// --- Autres fonctions (vous pouvez en ajouter ici) ---
// export * from './notifications/sendWelcomeEmail';
// export * from './database/cleanOldDocuments';
//# sourceMappingURL=index.js.map