"use strict";
/**
 * Cloud Function spécialisée pour les emprunts en retard
 * Logique métier avancée avec calculs de priorité et notifications
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendOverdueReminders = exports.getOverdueEmprunts = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const auth_1 = require("../utils/auth");
const firebase_functions_1 = require("firebase-functions");
const monitoring_1 = require("../utils/monitoring");
const db = admin.firestore();
/**
 * Calculer la priorité d'un emprunt en retard
 */
function calculatePriority(daysOverdue, estimatedValue) {
    // Priorité haute : > 7 jours de retard OU valeur > 1000€
    if (daysOverdue > 7 || (estimatedValue && estimatedValue > 1000)) {
        return "high";
    }
    // Priorité moyenne : 3-7 jours de retard OU valeur > 500€
    if (daysOverdue >= 3 || (estimatedValue && estimatedValue > 500)) {
        return "medium";
    }
    // Priorité basse : < 3 jours de retard
    return "low";
}
/**
 * Estimer la valeur d'un emprunt basée sur son matériel
 */
async function estimateEmpruntValue(empruntId) {
    try {
        const materielSnapshot = await db
            .collection("emprunts")
            .doc(empruntId)
            .collection("materiel")
            .get();
        let totalValue = 0;
        for (const materielDoc of materielSnapshot.docs) {
            const materielData = materielDoc.data();
            if (materielData.type === "stock") {
                // Récupérer le prix unitaire du stock
                const stockDoc = await db
                    .collection("stocks")
                    .doc(materielData.idMateriel)
                    .get();
                if (stockDoc.exists) {
                    const stockData = stockDoc.data();
                    const unitPrice = stockData?.prixUnitaire || 0;
                    const quantity = materielData.quantite || 1;
                    totalValue += unitPrice * quantity;
                }
            }
            else if (materielData.type === "module") {
                // Estimer la valeur d'un module (logique simplifiée)
                totalValue += 500; // Valeur moyenne estimée d'un module
            }
        }
        return totalValue;
    }
    catch (error) {
        firebase_functions_1.logger.warn("Impossible d'estimer la valeur de l'emprunt", { empruntId, error });
        return 0;
    }
}
/**
 * Récupérer les informations de contact de l'emprunteur
 */
async function getContactInfo(emprunteurName) {
    try {
        // Rechercher l'utilisateur par nom (logique simplifiée)
        const usersSnapshot = await db
            .collection("users")
            .where("displayName", "==", emprunteurName)
            .limit(1)
            .get();
        if (!usersSnapshot.empty) {
            const userData = usersSnapshot.docs[0].data();
            return {
                email: userData.email,
                phone: userData.phone
            };
        }
        return {};
    }
    catch (error) {
        firebase_functions_1.logger.warn("Impossible de récupérer les infos de contact", { emprunteurName, error });
        return {};
    }
}
/**
 * Cloud Function pour récupérer les emprunts en retard avec détails avancés
 */
exports.getOverdueEmprunts = functions
    .region("europe-west1")
    .https.onCall((0, monitoring_1.withMonitoring)('getOverdueEmprunts', async (data, context) => {
    // Bypass pour les tests
    if (process.env.NODE_ENV === "test") {
        return {
            emprunts: [],
            summary: {
                total: 0,
                high: 0,
                medium: 0,
                low: 0,
                averageDaysOverdue: 0
            },
            timestamp: new Date().toISOString()
        };
    }
    const startTime = Date.now();
    try {
        // Vérification des permissions
        (0, auth_1.checkRegisseurOrAdmin)(context);
        const { priority, minDaysOverdue = 0, limit = 50, includeContactInfo = false } = data || {};
        firebase_functions_1.logger.info("Récupération des emprunts en retard", {
            userId: context.auth?.uid,
            priority,
            minDaysOverdue,
            limit,
            includeContactInfo
        });
        // Requête optimisée pour les emprunts en retard
        const now = new Date();
        const snapshot = await db
            .collection("emprunts")
            .where("statut", "==", "Parti")
            .where("dateRetourPrevue", "<", now)
            .orderBy("dateRetourPrevue", "asc")
            .limit(limit)
            .get();
        // Traitement des emprunts avec calculs avancés
        const empruntsPromises = snapshot.docs.map(async (doc) => {
            const empruntData = doc.data();
            const dateRetourPrevue = empruntData.dateRetourPrevue.toDate();
            const dateDepart = empruntData.dateDepart.toDate();
            // Calculer les jours de retard
            const daysOverdue = Math.floor((now.getTime() - dateRetourPrevue.getTime()) / (1000 * 60 * 60 * 24));
            // Estimer la valeur si nécessaire pour la priorité
            const estimatedValue = await estimateEmpruntValue(doc.id);
            // Calculer la priorité
            const priority = calculatePriority(daysOverdue, estimatedValue);
            // Récupérer les infos de contact si demandées
            let contactInfo = {};
            if (includeContactInfo) {
                contactInfo = await getContactInfo(empruntData.emprunteur);
            }
            return {
                id: doc.id,
                nom: empruntData.nom,
                lieu: empruntData.lieu,
                emprunteur: empruntData.emprunteur,
                secteur: empruntData.secteur || "Non spécifié",
                dateRetourPrevue,
                dateDepart,
                daysOverdue,
                priority,
                estimatedValue: estimatedValue > 0 ? estimatedValue : undefined,
                contactInfo: includeContactInfo ? contactInfo : undefined
            };
        });
        const emprunts = await Promise.all(empruntsPromises);
        // Filtrer selon les critères
        let filteredEmprunts = emprunts.filter(emprunt => emprunt.daysOverdue >= minDaysOverdue);
        if (priority) {
            filteredEmprunts = filteredEmprunts.filter(emprunt => emprunt.priority === priority);
        }
        // Calculer le résumé
        const summary = {
            total: filteredEmprunts.length,
            high: filteredEmprunts.filter(e => e.priority === "high").length,
            medium: filteredEmprunts.filter(e => e.priority === "medium").length,
            low: filteredEmprunts.filter(e => e.priority === "low").length,
            averageDaysOverdue: filteredEmprunts.length > 0
                ? Math.round(filteredEmprunts.reduce((sum, e) => sum + e.daysOverdue, 0) / filteredEmprunts.length)
                : 0
        };
        const executionTime = Date.now() - startTime;
        // Envoyer le nombre d'emprunts en retard critique au monitoring
        await (0, monitoring_1.sendCriticalOverdueCount)(summary.high);
        firebase_functions_1.logger.info("Emprunts en retard récupérés avec succès", {
            userId: context.auth?.uid,
            totalEmprunts: emprunts.length,
            filteredEmprunts: filteredEmprunts.length,
            summary,
            executionTimeMs: executionTime
        });
        return {
            emprunts: filteredEmprunts,
            summary,
            timestamp: new Date().toISOString(),
            performance: {
                executionTimeMs: executionTime,
                includeContactInfo,
                estimatedValues: emprunts.filter(e => e.estimatedValue).length
            }
        };
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        firebase_functions_1.logger.error("Erreur lors de la récupération des emprunts en retard", {
            userId: context.auth?.uid,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            executionTimeMs: executionTime
        });
        throw new functions.https.HttpsError("internal", "Erreur lors de la récupération des emprunts en retard");
    }
}));
/**
 * Cloud Function pour envoyer des rappels automatiques (tâche planifiée)
 */
exports.sendOverdueReminders = functions
    .region("europe-west1")
    .pubsub.schedule("every day 09:00")
    .timeZone("Europe/Paris")
    .onRun(async (context) => {
    try {
        firebase_functions_1.logger.info("Envoi des rappels automatiques pour emprunts en retard");
        // Récupérer les emprunts en retard de priorité haute
        const now = new Date();
        const snapshot = await db
            .collection("emprunts")
            .where("statut", "==", "Parti")
            .where("dateRetourPrevue", "<", now)
            .get();
        const highPriorityOverdue = [];
        for (const doc of snapshot.docs) {
            const empruntData = doc.data();
            const dateRetourPrevue = empruntData.dateRetourPrevue.toDate();
            const daysOverdue = Math.floor((now.getTime() - dateRetourPrevue.getTime()) / (1000 * 60 * 60 * 24));
            if (daysOverdue > 7) { // Priorité haute
                highPriorityOverdue.push({
                    id: doc.id,
                    nom: empruntData.nom,
                    emprunteur: empruntData.emprunteur,
                    daysOverdue
                });
            }
        }
        if (highPriorityOverdue.length > 0) {
            firebase_functions_1.logger.warn("Emprunts en retard critique détectés", {
                count: highPriorityOverdue.length,
                emprunts: highPriorityOverdue
            });
            // Ici, on pourrait envoyer des emails, SMS, notifications push, etc.
            // await sendOverdueNotifications(highPriorityOverdue);
        }
        return { highPriorityOverdueCount: highPriorityOverdue.length };
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de l'envoi des rappels automatiques", { error });
        throw error;
    }
});
