/**
 * Gestionnaire principal du dashboard SIGMA
 * Gestion des listeners temps-réel avec pagination et optimisations
 */

class DashboardManager {
  constructor() {
    this.listeners = new Map();
    this.lastDocs = new Map();
    this.debounceTimers = new Map();
    this.isInitialized = false;
    this.refreshIndicator = null;

    // Gestionnaires associés
    this.paginationManager = new PaginationManager();
    this.dashboardUI = new DashboardUI();
    
    // Configuration des tableaux
    this.tableConfigs = {
      stockAlerts: {
        name: 'Alertes Stock',
        icon: '🚨',
        containerId: 'stock-alerts-table',
        limit: 20,
        refreshInterval: 30000 // 30 secondes
      },
      missingMaterial: {
        name: '<PERSON><PERSON><PERSON>quant',
        icon: '❌',
        containerId: 'missing-material-table',
        limit: 20,
        refreshInterval: 60000 // 1 minute
      },
      overdueEmprunts: {
        name: 'Emprunts en Retard',
        icon: '⏰',
        containerId: 'overdue-emprunts-table',
        limit: 20,
        refreshInterval: 30000 // 30 secondes
      },
      upcomingEmprunts: {
        name: 'Prochains Emprunts',
        icon: '📅',
        containerId: 'upcoming-emprunts-table',
        limit: 20,
        refreshInterval: 300000 // 5 minutes
      },
      nonOpModules: {
        name: 'Modules Non Opérationnels',
        icon: '📌',
        containerId: 'non-op-modules-table',
        limit: 20,
        refreshInterval: 120000 // 2 minutes
      },
      nonOpMaterial: {
        name: 'Matériel Non Opérationnel',
        icon: '🔧',
        containerId: 'non-op-material-table',
        limit: 20,
        refreshInterval: 120000 // 2 minutes
      },
      pendingEmprunts: {
        name: 'Emprunts en Attente',
        icon: '📋',
        containerId: 'pending-emprunts-table',
        limit: 20,
        refreshInterval: 60000 // 1 minute
      }
    };
  }

  /**
   * Initialiser le dashboard
   */
  async initialize() {
    try {
      console.log('🚀 Initialisation du dashboard SIGMA...');
      
      // Vérifier l'authentification
      const user = firebase.auth().currentUser;
      if (!user) {
        throw new Error('Utilisateur non authentifié');
      }

      // Vérifier les permissions
      const idTokenResult = await user.getIdTokenResult();
      const userRole = idTokenResult.claims.role;
      
      if (!['regisseur', 'admin'].includes(userRole)) {
        throw new Error('Permissions insuffisantes pour accéder au dashboard');
      }

      // Initialiser l'indicateur de rafraîchissement
      this.initializeRefreshIndicator();

      // Initialiser la pagination pour tous les tableaux
      this.initializePagination();

      // Configurer les listeners temps-réel
      this.setupRealtimeListeners();

      // Charger les données initiales
      await this.loadInitialData();

      this.isInitialized = true;
      console.log('✅ Dashboard initialisé avec succès');

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation du dashboard:', error);
      this.showError('Erreur d\'initialisation', error.message);
    }
  }

  /**
   * Configurer tous les listeners temps-réel
   */
  setupRealtimeListeners() {
    console.log('🔗 Configuration des listeners temps-réel...');

    // Listener pour alertes stock
    this.setupStockAlertsListener();
    
    // Listener pour matériel manquant
    this.setupMissingMaterialListener();
    
    // Listener pour emprunts en retard
    this.setupOverdueEmpruntsListener();
    
    // Listener pour prochains emprunts
    this.setupUpcomingEmpruntsListener();
    
    // Listener pour modules non opérationnels
    this.setupNonOpModulesListener();
    
    // Listener pour matériel non opérationnel
    this.setupNonOpMaterialListener();
    
    // Listener pour emprunts en attente
    this.setupPendingEmpruntsListener();

    console.log('✅ Tous les listeners configurés');
  }

  /**
   * Listener pour alertes stock (quantité <= seuil)
   */
  setupStockAlertsListener() {
    const query = firebase.firestore()
      .collection('stocks')
      .where('estOperationnel', '==', true)
      .orderBy('quantite', 'asc')
      .limit(this.tableConfigs.stockAlerts.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleStockAlertsUpdate(snapshot),
      (error) => this.handleError('stock-alerts', error)
    );

    this.listeners.set('stockAlerts', unsubscribe);
  }

  /**
   * Listener pour matériel manquant (à commander)
   */
  setupMissingMaterialListener() {
    const query = firebase.firestore()
      .collection('stocks')
      .where('aCommander', '==', true)
      .orderBy('updatedAt', 'desc')
      .limit(this.tableConfigs.missingMaterial.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleMissingMaterialUpdate(snapshot),
      (error) => this.handleError('missing-material', error)
    );

    this.listeners.set('missingMaterial', unsubscribe);
  }

  /**
   * Listener pour emprunts en retard
   */
  setupOverdueEmpruntsListener() {
    const now = new Date();
    const query = firebase.firestore()
      .collection('emprunts')
      .where('statut', '==', 'Parti')
      .where('dateRetourPrevue', '<', now)
      .orderBy('dateRetourPrevue', 'asc')
      .limit(this.tableConfigs.overdueEmprunts.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleOverdueEmpruntsUpdate(snapshot),
      (error) => this.handleError('overdue-emprunts', error)
    );

    this.listeners.set('overdueEmprunts', unsubscribe);
  }

  /**
   * Listener pour prochains emprunts (< 30 jours)
   */
  setupUpcomingEmpruntsListener() {
    const now = new Date();
    const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    const query = firebase.firestore()
      .collection('emprunts')
      .where('statut', 'in', ['Pas prêt', 'Prêt'])
      .where('dateDepart', '>=', now)
      .where('dateDepart', '<=', in30Days)
      .orderBy('dateDepart', 'asc')
      .limit(this.tableConfigs.upcomingEmprunts.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleUpcomingEmpruntsUpdate(snapshot),
      (error) => this.handleError('upcoming-emprunts', error)
    );

    this.listeners.set('upcomingEmprunts', unsubscribe);
  }

  /**
   * Listener pour modules non opérationnels
   */
  setupNonOpModulesListener() {
    const query = firebase.firestore()
      .collection('modules')
      .where('estPret', '==', false)
      .orderBy('updatedAt', 'desc')
      .limit(this.tableConfigs.nonOpModules.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleNonOpModulesUpdate(snapshot),
      (error) => this.handleError('non-op-modules', error)
    );

    this.listeners.set('nonOpModules', unsubscribe);
  }

  /**
   * Listener pour matériel non opérationnel
   */
  setupNonOpMaterialListener() {
    const query = firebase.firestore()
      .collection('stocks')
      .where('estOperationnel', '==', false)
      .orderBy('updatedAt', 'desc')
      .limit(this.tableConfigs.nonOpMaterial.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleNonOpMaterialUpdate(snapshot),
      (error) => this.handleError('non-op-material', error)
    );

    this.listeners.set('nonOpMaterial', unsubscribe);
  }

  /**
   * Listener pour emprunts en attente (non inventoriés/facturés)
   */
  setupPendingEmpruntsListener() {
    // Listener pour emprunts non inventoriés
    const nonInventoriedQuery = firebase.firestore()
      .collection('emprunts')
      .where('statut', '==', 'Revenu')
      .where('estInventorie', '==', false)
      .orderBy('dateRetourEffective', 'desc')
      .limit(10);

    const unsubscribe1 = nonInventoriedQuery.onSnapshot(
      (snapshot) => this.handlePendingEmpruntsUpdate(snapshot, 'inventaire'),
      (error) => this.handleError('pending-emprunts-inventory', error)
    );

    // Listener pour emprunts non facturés
    const nonBilledQuery = firebase.firestore()
      .collection('emprunts')
      .where('statut', '==', 'Revenu')
      .where('estFacture', '==', false)
      .orderBy('dateRetourEffective', 'desc')
      .limit(10);

    const unsubscribe2 = nonBilledQuery.onSnapshot(
      (snapshot) => this.handlePendingEmpruntsUpdate(snapshot, 'facturation'),
      (error) => this.handleError('pending-emprunts-billing', error)
    );

    this.listeners.set('pendingEmpruntsInventory', unsubscribe1);
    this.listeners.set('pendingEmpruntsBilling', unsubscribe2);
  }

  /**
   * Gestionnaire pour mise à jour alertes stock
   */
  handleStockAlertsUpdate(snapshot) {
    this.debounceUpdate('stockAlerts', () => {
      const alerts = snapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter(stock => stock.quantite <= stock.seuilAlerte);
      
      this.dashboardUI.updateStockAlertsTable(alerts);
      this.updateRefreshIndicator('stockAlerts');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour matériel manquant
   */
  handleMissingMaterialUpdate(snapshot) {
    this.debounceUpdate('missingMaterial', () => {
      const materials = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      this.dashboardUI.updateMissingMaterialTable(materials);
      this.updateRefreshIndicator('missingMaterial');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour emprunts en retard
   */
  handleOverdueEmpruntsUpdate(snapshot) {
    this.debounceUpdate('overdueEmprunts', () => {
      const emprunts = snapshot.docs.map(doc => {
        const data = doc.data();
        const dateRetourPrevue = data.dateRetourPrevue.toDate();
        const daysOverdue = Math.floor((new Date() - dateRetourPrevue) / (1000 * 60 * 60 * 24));

        return {
          id: doc.id,
          ...data,
          dateRetourPrevue,
          daysOverdue
        };
      });

      this.dashboardUI.updateOverdueEmpruntsTable(emprunts);
      this.updateRefreshIndicator('overdueEmprunts');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour prochains emprunts
   */
  handleUpcomingEmpruntsUpdate(snapshot) {
    this.debounceUpdate('upcomingEmprunts', () => {
      const emprunts = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          dateDepart: data.dateDepart.toDate(),
          dateRetourPrevue: data.dateRetourPrevue.toDate()
        };
      });

      this.dashboardUI.updateUpcomingEmpruntsTable(emprunts);
      this.updateRefreshIndicator('upcomingEmprunts');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour modules non opérationnels
   */
  handleNonOpModulesUpdate(snapshot) {
    this.debounceUpdate('nonOpModules', () => {
      const modules = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      this.dashboardUI.updateNonOpModulesTable(modules);
      this.updateRefreshIndicator('nonOpModules');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour matériel non opérationnel
   */
  handleNonOpMaterialUpdate(snapshot) {
    this.debounceUpdate('nonOpMaterial', () => {
      const materials = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      this.dashboardUI.updateNonOpMaterialTable(materials);
      this.updateRefreshIndicator('nonOpMaterial');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour emprunts en attente
   */
  handlePendingEmpruntsUpdate(snapshot, type) {
    this.debounceUpdate(`pendingEmprunts_${type}`, () => {
      const emprunts = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        pendingType: type
      }));

      // Fusionner avec l'autre type si nécessaire
      this.mergePendingEmprunts(emprunts, type);
      this.updateRefreshIndicator('pendingEmprunts');
    }, 200);
  }

  /**
   * Fusionner les emprunts en attente (inventaire + facturation)
   */
  mergePendingEmprunts(newEmprunts, type) {
    if (!this.pendingEmpruntsCache) {
      this.pendingEmpruntsCache = { inventaire: [], facturation: [] };
    }

    this.pendingEmpruntsCache[type] = newEmprunts;

    // Fusionner et dédupliquer
    const allPending = [...this.pendingEmpruntsCache.inventaire, ...this.pendingEmpruntsCache.facturation];
    const uniquePending = allPending.filter(
      (emprunt, index, self) => index === self.findIndex(e => e.id === emprunt.id)
    );

    this.dashboardUI.updatePendingEmpruntsTable(uniquePending.slice(0, 20));
  }

  /**
   * Initialiser la pagination pour tous les tableaux
   */
  initializePagination() {
    Object.keys(this.tableConfigs).forEach(tableId => {
      const config = this.tableConfigs[tableId];
      this.paginationManager.initializePagination(tableId, config.limit);
    });
  }

  /**
   * Debouncing pour éviter trop de mises à jour UI
   */
  debounceUpdate(key, callback, delay) {
    clearTimeout(this.debounceTimers.get(key));
    this.debounceTimers.set(key, setTimeout(callback, delay));
  }

  /**
   * Charger les données initiales via Cloud Function
   */
  async loadInitialData() {
    try {
      console.log('📊 Chargement des données initiales...');
      
      const getDashboardData = firebase.functions().httpsCallable('getDashboardData');
      const result = await getDashboardData();
      
      const data = result.data;
      console.log('✅ Données initiales chargées:', data.performance);
      
      // Afficher les métriques de performance
      this.displayPerformanceMetrics(data.performance);
      
    } catch (error) {
      console.error('❌ Erreur lors du chargement initial:', error);
      this.showError('Chargement initial', error.message);
    }
  }

  /**
   * Gérer les erreurs des listeners
   */
  handleError(listenerId, error) {
    console.error(`❌ Erreur listener ${listenerId}:`, error);
    
    // Afficher une notification d'erreur
    this.showError(`Erreur ${listenerId}`, 'Problème de connexion temps-réel');
    
    // Tentative de reconnexion après 5 secondes
    setTimeout(() => {
      console.log(`🔄 Tentative de reconnexion pour ${listenerId}...`);
      this.setupRealtimeListeners();
    }, 5000);
  }

  /**
   * Nettoyer tous les listeners
   */
  cleanup() {
    console.log('🧹 Nettoyage des listeners...');
    
    this.listeners.forEach((unsubscribe, key) => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    });
    
    this.listeners.clear();
    this.debounceTimers.clear();
    
    console.log('✅ Nettoyage terminé');
  }

  /**
   * Initialiser l'indicateur de rafraîchissement
   */
  initializeRefreshIndicator() {
    this.refreshIndicator = document.getElementById('refresh-indicator');
    if (this.refreshIndicator) {
      this.refreshIndicator.innerHTML = '🔄 Temps-réel actif';
      this.refreshIndicator.className = 'refresh-indicator active';
    }
  }

  /**
   * Mettre à jour l'indicateur de rafraîchissement
   */
  updateRefreshIndicator(tableId) {
    if (this.refreshIndicator) {
      this.refreshIndicator.innerHTML = `🔄 Mis à jour: ${new Date().toLocaleTimeString()}`;
      this.refreshIndicator.className = 'refresh-indicator updated';
      
      // Retour à l'état normal après 2 secondes
      setTimeout(() => {
        this.refreshIndicator.className = 'refresh-indicator active';
      }, 2000);
    }
  }

  /**
   * Afficher une erreur à l'utilisateur
   */
  showError(title, message) {
    // Ici, on pourrait utiliser une bibliothèque de notifications
    console.error(`${title}: ${message}`);
    
    // Affichage simple pour le moment
    const errorContainer = document.getElementById('error-container');
    if (errorContainer) {
      errorContainer.innerHTML = `
        <div class="alert alert-error">
          <strong>${title}</strong>: ${message}
        </div>
      `;
      
      // Masquer après 5 secondes
      setTimeout(() => {
        errorContainer.innerHTML = '';
      }, 5000);
    }
  }

  /**
   * Afficher les métriques de performance
   */
  displayPerformanceMetrics(performance) {
    const metricsContainer = document.getElementById('performance-metrics');
    if (metricsContainer && performance) {
      metricsContainer.innerHTML = `
        <div class="metrics">
          <span>⏱️ ${performance.executionTimeMs}ms</span>
          <span>📊 ${performance.totalQueries} requêtes</span>
        </div>
      `;
    }
  }
}

// Export pour utilisation globale
window.DashboardManager = DashboardManager;
