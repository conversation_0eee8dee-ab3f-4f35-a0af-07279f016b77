import ftest from "firebase-functions-test";
import { generateEmpruntLabels } from "../emprunts/generateEmpruntLabels";
import { adminDb } from "../tests/setupTests";

describe("Performance Tests", () => {
  let testEnv: any;

  beforeAll(() => {
    testEnv = ftest();
    // Initialiser Firebase Admin est maintenant fait dans setupTests
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    testEnv.cleanup();
  });

  it("devrait générer un PDF en moins de 3 secondes", async () => {
    const wrapped = testEnv.wrap(generateEmpruntLabels);
    const data = { empruntId: "test-id" };
    const context = { auth: { uid: "test-uid", token: { role: "admin" } } };

    const startTime = Date.now();
    await wrapped(data, context);
    const duration = Date.now() - startTime;

    expect(duration).toBeLessThan(3000);
  });
});
