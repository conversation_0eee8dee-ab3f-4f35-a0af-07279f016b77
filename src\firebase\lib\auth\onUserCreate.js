"use strict";
/**
 * Cloud Function trigger pour la création automatique d'utilisateurs
 * Assigne le rôle par défaut et initialise le profil utilisateur
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.onUserCreate = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const auth_1 = require("firebase-admin/auth");
const firestore_1 = require("firebase-admin/firestore");
const logger = __importStar(require("firebase-functions/logger"));
/**
 * Trigger automatique lors de la création d'un utilisateur
 *
 * Actions effectuées:
 * - Assignation du rôle 'utilisateur' par défaut via Custom Claims
 * - Création du document utilisateur dans Firestore
 * - Logging pour audit et monitoring
 *
 * Sécurité:
 * - Transaction Firestore pour cohérence
 * - Gestion d'erreurs robuste
 * - Logging complet pour audit
 */
exports.onUserCreate = functions
    .auth.user().onCreate(async (user) => {
    var _a, _b, _c, _d;
    const startTime = Date.now();
    try {
        logger.info('👤 Nouveau utilisateur créé', {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
            provider: (_b = (_a = user.providerData) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.providerId
        });
        // 1. Assignation du rôle par défaut via Custom Claims
        const auth = (0, auth_1.getAuth)();
        const defaultRole = 'utilisateur';
        const customClaims = {
            role: defaultRole,
            roleAssignedAt: new Date().toISOString(),
            roleAssignedBy: 'system', // Assigné automatiquement par le système
            accountCreatedAt: new Date().toISOString()
        };
        await auth.setCustomUserClaims(user.uid, customClaims);
        logger.info('✅ Custom Claims assignés', {
            uid: user.uid,
            role: defaultRole
        });
        // 2. Création du document utilisateur dans Firestore
        const db = (0, firestore_1.getFirestore)();
        // Utiliser une transaction pour garantir la cohérence
        await db.runTransaction(async (transaction) => {
            var _a, _b;
            const userRef = db.collection('users').doc(user.uid);
            // Vérifier si le document existe déjà (cas rare mais possible)
            const existingDoc = await transaction.get(userRef);
            if (existingDoc.exists) {
                logger.warn('⚠️ Document utilisateur existe déjà', {
                    uid: user.uid,
                    email: user.email
                });
                return;
            }
            // Créer le document utilisateur
            const userData = {
                uid: user.uid,
                email: user.email || '',
                displayName: user.displayName || '',
                photoURL: user.photoURL || '',
                role: defaultRole,
                // Métadonnées de création
                createdAt: new Date(),
                createdBy: 'system',
                updatedAt: new Date(),
                updatedBy: 'system',
                // Historique des rôles
                roleHistory: [
                    {
                        previousRole: null,
                        newRole: defaultRole,
                        changedAt: new Date(),
                        changedBy: 'system',
                        changedByEmail: 'system',
                        reason: 'Création automatique du compte'
                    }
                ],
                // Informations du provider
                provider: ((_b = (_a = user.providerData) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.providerId) || 'unknown',
                // Statut du compte
                isActive: true,
                emailVerified: user.emailVerified || false,
                // Préférences par défaut
                preferences: {
                    language: 'fr',
                    notifications: {
                        email: true,
                        browser: true
                    },
                    theme: 'light'
                },
                // Statistiques d'utilisation (initialisées à zéro)
                stats: {
                    loginCount: 0,
                    lastLoginAt: null,
                    empruntsCount: 0,
                    lastActivityAt: new Date()
                }
            };
            transaction.set(userRef, userData);
            logger.info('✅ Document utilisateur créé dans Firestore', {
                uid: user.uid,
                email: user.email,
                role: defaultRole
            });
        });
        // 3. Logging de succès avec métriques
        const executionTime = Date.now() - startTime;
        logger.info('🎉 Initialisation utilisateur terminée avec succès', {
            uid: user.uid,
            email: user.email,
            role: defaultRole,
            executionTimeMs: executionTime,
            provider: (_d = (_c = user.providerData) === null || _c === void 0 ? void 0 : _c[0]) === null || _d === void 0 ? void 0 : _d.providerId
        });
        // 4. Optionnel: Envoyer un email de bienvenue (à implémenter plus tard)
        // await sendWelcomeEmail(user.email, user.displayName);
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        logger.error('❌ Erreur lors de l\'initialisation de l\'utilisateur', {
            uid: user.uid,
            email: user.email,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            executionTimeMs: executionTime
        });
        // Ne pas faire échouer la création du compte même si l'initialisation échoue
        // L'utilisateur pourra toujours se connecter et les données seront créées plus tard
        // Optionnel: Envoyer une alerte aux administrateurs
        // await notifyAdminsOfUserCreationError(user.uid, error);
    }
});
//# sourceMappingURL=onUserCreate.js.map