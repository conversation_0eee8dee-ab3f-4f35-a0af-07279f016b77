/**
 * Gestion de l'authentification pour SIGMA
 * Fonctions pour la connexion Google, déconnexion et gestion des états
 */

class AuthManager {
    constructor() {
        this.auth = null;
        this.db = null;
        this.functions = null;
        this.currentUser = null;
        this.userRole = null;
        
        // Éléments DOM
        this.elements = {
            loginForm: document.getElementById('login-form'),
            loadingState: document.getElementById('loading-state'),
            googleSigninBtn: document.getElementById('google-signin-btn'),
            errorMessage: document.getElementById('error-message'),
            successMessage: document.getElementById('success-message'),
            errorText: document.getElementById('error-text'),
            successText: document.getElementById('success-text')
        };
        
        this.init();
    }

    /**
     * Initialisation du gestionnaire d'authentification
     */
    async init() {
        try {
            // Attendre que Firebase soit initialisé
            await this.waitForFirebase();
            
            // Récupérer les services Firebase
            const services = window.firebaseServices;
            this.auth = services.auth;
            this.db = services.db;
            this.functions = services.functions;
            
            // Configurer les écouteurs d'événements
            this.setupEventListeners();
            
            // Écouter les changements d'état d'authentification
            this.auth.onAuthStateChanged(this.handleAuthStateChange.bind(this));
            
            console.log('✅ AuthManager initialisé');
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation d\'AuthManager:', error);
            this.showError('Erreur d\'initialisation de l\'authentification');
        }
    }

    /**
     * Attendre que Firebase soit disponible
     */
    waitForFirebase() {
        return new Promise((resolve, reject) => {
            const checkFirebase = () => {
                if (window.firebaseServices) {
                    resolve();
                } else {
                    setTimeout(checkFirebase, 100);
                }
            };
            checkFirebase();
            
            // Timeout après 10 secondes
            setTimeout(() => reject(new Error('Firebase non disponible')), 10000);
        });
    }

    /**
     * Configuration des écouteurs d'événements
     */
    setupEventListeners() {
        if (this.elements.googleSigninBtn) {
            this.elements.googleSigninBtn.addEventListener('click', this.signInWithGoogle.bind(this));
        }
    }

    /**
     * Connexion avec Google
     */
    async signInWithGoogle() {
        try {
            this.showLoading(true);
            this.hideMessages();
            
            const provider = window.firebaseServices.googleProvider;
            const result = await this.auth.signInWithPopup(provider);
            
            console.log('✅ Connexion Google réussie:', result.user.email);
            this.showSuccess('Connexion réussie ! Redirection en cours...');
            
            // Redirection après un court délai
            setTimeout(() => {
                this.redirectToApp();
            }, 1500);
            
        } catch (error) {
            console.error('❌ Erreur de connexion Google:', error);
            this.handleAuthError(error);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Déconnexion
     */
    async signOut() {
        try {
            await this.auth.signOut();
            console.log('✅ Déconnexion réussie');
            this.showSuccess('Déconnexion réussie');
            
            // Redirection vers la page de connexion
            setTimeout(() => {
                window.location.href = '/html/login.html';
            }, 1000);
            
        } catch (error) {
            console.error('❌ Erreur de déconnexion:', error);
            this.showError('Erreur lors de la déconnexion');
        }
    }

    /**
     * Gestion des changements d'état d'authentification
     */
    async handleAuthStateChange(user) {
        if (user) {
            console.log('👤 Utilisateur connecté:', user.email);
            this.currentUser = user;
            
            // Récupérer le rôle de l'utilisateur
            await this.getUserRole();
            
            // Si on est sur la page de connexion et que l'utilisateur est connecté, rediriger
            if (window.location.pathname.includes('login.html')) {
                this.redirectToApp();
            }
        } else {
            console.log('👤 Utilisateur déconnecté');
            this.currentUser = null;
            this.userRole = null;
            
            // Si on n'est pas sur la page de connexion, rediriger
            if (!window.location.pathname.includes('login.html')) {
                window.location.href = '/html/login.html';
            }
        }
    }

    /**
     * Récupérer le rôle de l'utilisateur depuis les Custom Claims
     */
    async getUserRole() {
        try {
            if (this.currentUser) {
                const idTokenResult = await this.currentUser.getIdTokenResult();
                this.userRole = idTokenResult.claims.role || 'utilisateur';
                console.log('👤 Rôle utilisateur:', this.userRole);
            }
        } catch (error) {
            console.error('❌ Erreur lors de la récupération du rôle:', error);
            this.userRole = 'utilisateur'; // Rôle par défaut
        }
    }

    /**
     * Redirection vers l'application principale
     */
    redirectToApp() {
        // Rediriger vers le dashboard principal
        window.location.href = '/';
    }

    /**
     * Gestion des erreurs d'authentification
     */
    handleAuthError(error) {
        let message = 'Une erreur est survenue lors de la connexion';
        
        switch (error.code) {
            case 'auth/popup-closed-by-user':
                message = 'Connexion annulée par l\'utilisateur';
                break;
            case 'auth/popup-blocked':
                message = 'Popup bloquée par le navigateur. Veuillez autoriser les popups.';
                break;
            case 'auth/network-request-failed':
                message = 'Erreur de réseau. Vérifiez votre connexion internet.';
                break;
            case 'auth/too-many-requests':
                message = 'Trop de tentatives. Veuillez réessayer plus tard.';
                break;
            default:
                console.error('Code d\'erreur:', error.code);
        }
        
        this.showError(message);
    }

    /**
     * Afficher/masquer l'état de chargement
     */
    showLoading(show) {
        if (this.elements.loadingState && this.elements.loginForm) {
            this.elements.loadingState.style.display = show ? 'block' : 'none';
            this.elements.loginForm.style.display = show ? 'none' : 'block';
        }
    }

    /**
     * Afficher un message d'erreur
     */
    showError(message) {
        if (this.elements.errorMessage && this.elements.errorText) {
            this.elements.errorText.textContent = message;
            this.elements.errorMessage.style.display = 'flex';
            this.elements.successMessage.style.display = 'none';
        }
    }

    /**
     * Afficher un message de succès
     */
    showSuccess(message) {
        if (this.elements.successMessage && this.elements.successText) {
            this.elements.successText.textContent = message;
            this.elements.successMessage.style.display = 'flex';
            this.elements.errorMessage.style.display = 'none';
        }
    }

    /**
     * Masquer tous les messages
     */
    hideMessages() {
        if (this.elements.errorMessage && this.elements.successMessage) {
            this.elements.errorMessage.style.display = 'none';
            this.elements.successMessage.style.display = 'none';
        }
    }

    /**
     * Vérifier si l'utilisateur est connecté
     */
    isAuthenticated() {
        return !!this.currentUser;
    }

    /**
     * Vérifier si l'utilisateur a un rôle spécifique
     */
    hasRole(role) {
        return this.userRole === role || this.userRole === 'admin';
    }

    /**
     * Obtenir les informations de l'utilisateur actuel
     */
    getCurrentUser() {
        return {
            user: this.currentUser,
            role: this.userRole
        };
    }
}

// Initialiser le gestionnaire d'authentification quand le DOM est prêt
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});

// Export pour utilisation dans d'autres fichiers
window.AuthManager = AuthManager;
