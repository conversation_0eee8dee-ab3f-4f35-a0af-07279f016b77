"use strict";
/**
 * Cloud Function spécialisée pour les alertes stock critiques
 * Optimisée pour les notifications et le monitoring
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkCriticalStockLevels = exports.getStockAlerts = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const auth_1 = require("../utils/auth");
const firebase_functions_1 = require("firebase-functions");
const monitoring_1 = require("../utils/monitoring");
const db = admin.firestore();
/**
 * Calculer la sévérité d'une alerte stock
 */
function calculateSeverity(quantite, seuilAlerte) {
    const percentage = (quantite / seuilAlerte) * 100;
    if (percentage <= 25)
        return "critical"; // 25% ou moins du seuil
    if (percentage <= 50)
        return "warning"; // 50% ou moins du seuil
    return "info"; // Au-dessus de 50% du seuil
}
/**
 * Estimer les jours avant épuisement (basé sur consommation moyenne)
 */
async function estimateDaysUntilEmpty(stockId, currentQuantity) {
    try {
        // Récupérer les mouvements des 30 derniers jours
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const movementsSnapshot = await db
            .collection("stocks")
            .doc(stockId)
            .collection("mouvements")
            .where("type", "==", "sortie")
            .where("date", ">=", thirtyDaysAgo)
            .get();
        if (movementsSnapshot.empty)
            return undefined;
        // Calculer la consommation moyenne par jour
        const totalConsumption = movementsSnapshot.docs.reduce((sum, doc) => sum + (doc.data().quantite || 0), 0);
        const averageDailyConsumption = totalConsumption / 30;
        if (averageDailyConsumption <= 0)
            return undefined;
        return Math.floor(currentQuantity / averageDailyConsumption);
    }
    catch (error) {
        firebase_functions_1.logger.warn("Impossible de calculer les jours avant épuisement", { stockId, error });
        return undefined;
    }
}
/**
 * Cloud Function pour récupérer les alertes stock avec détails avancés
 */
exports.getStockAlerts = functions
    .region("europe-west1")
    .https.onCall((0, monitoring_1.withMonitoring)('getStockAlerts', async (data, context) => {
    // Bypass pour les tests
    if (process.env.NODE_ENV === "test") {
        return {
            alerts: [],
            summary: {
                total: 0,
                critical: 0,
                warning: 0,
                info: 0
            },
            timestamp: new Date().toISOString()
        };
    }
    const startTime = Date.now();
    try {
        // Vérification des permissions
        (0, auth_1.checkRegisseurOrAdmin)(context);
        const { severity, limit = 50, includeProjections = false } = data || {};
        firebase_functions_1.logger.info("Récupération des alertes stock", {
            userId: context.auth?.uid,
            severity,
            limit,
            includeProjections
        });
        // Requête optimisée pour les stocks critiques
        let query = db
            .collection("stocks")
            .where("estOperationnel", "==", true)
            .orderBy("quantite", "asc")
            .limit(limit);
        const snapshot = await query.get();
        // Traitement des alertes avec calculs avancés
        const alertsPromises = snapshot.docs
            .map(doc => ({
            id: doc.id,
            ...doc.data()
        }))
            .filter((stock) => stock.quantite <= stock.seuilAlerte)
            .map(async (stock) => {
            const severity = calculateSeverity(stock.quantite, stock.seuilAlerte);
            const percentageRemaining = (stock.quantite / stock.seuilAlerte) * 100;
            let daysUntilEmpty;
            if (includeProjections) {
                daysUntilEmpty = await estimateDaysUntilEmpty(stock.id, stock.quantite);
            }
            return {
                id: stock.id,
                nom: stock.nom,
                quantite: stock.quantite,
                seuilAlerte: stock.seuilAlerte,
                localisation: stock.localisation || "Non spécifiée",
                categorie: stock.categorie || "Général",
                severity,
                percentageRemaining: Math.round(percentageRemaining),
                daysUntilEmpty
            };
        });
        const alerts = await Promise.all(alertsPromises);
        // Filtrer par sévérité si spécifiée
        const filteredAlerts = severity
            ? alerts.filter(alert => alert.severity === severity)
            : alerts;
        // Calculer le résumé
        const summary = {
            total: alerts.length,
            critical: alerts.filter(a => a.severity === "critical").length,
            warning: alerts.filter(a => a.severity === "warning").length,
            info: alerts.filter(a => a.severity === "info").length
        };
        const executionTime = Date.now() - startTime;
        // Envoyer le nombre d'alertes critiques au monitoring
        await (0, monitoring_1.sendCriticalStockCount)(summary.critical);
        firebase_functions_1.logger.info("Alertes stock récupérées avec succès", {
            userId: context.auth?.uid,
            totalAlerts: alerts.length,
            filteredAlerts: filteredAlerts.length,
            summary,
            executionTimeMs: executionTime
        });
        return {
            alerts: filteredAlerts,
            summary,
            timestamp: new Date().toISOString(),
            performance: {
                executionTimeMs: executionTime,
                includeProjections
            }
        };
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        firebase_functions_1.logger.error("Erreur lors de la récupération des alertes stock", {
            userId: context.auth?.uid,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            executionTimeMs: executionTime
        });
        throw new functions.https.HttpsError("internal", "Erreur lors de la récupération des alertes stock");
    }
}));
/**
 * Cloud Function pour vérifier les seuils critiques (pour monitoring automatique)
 */
exports.checkCriticalStockLevels = functions
    .region("europe-west1")
    .pubsub.schedule("every 1 hours")
    .onRun(async (context) => {
    try {
        firebase_functions_1.logger.info("Vérification automatique des niveaux de stock critiques");
        // Récupérer tous les stocks critiques (quantité <= 25% du seuil)
        const snapshot = await db
            .collection("stocks")
            .where("estOperationnel", "==", true)
            .get();
        const criticalStocks = snapshot.docs
            .map(doc => ({ id: doc.id, ...doc.data() }))
            .filter((stock) => {
            const percentage = (stock.quantite / stock.seuilAlerte) * 100;
            return percentage <= 25; // Critique si <= 25% du seuil
        });
        if (criticalStocks.length > 0) {
            firebase_functions_1.logger.warn("Stocks critiques détectés", {
                count: criticalStocks.length,
                stocks: criticalStocks.map((s) => ({
                    id: s.id,
                    nom: s.nom,
                    quantite: s.quantite,
                    seuil: s.seuilAlerte
                }))
            });
            // Ici, on pourrait envoyer des notifications push, emails, etc.
            // await sendCriticalStockNotifications(criticalStocks);
        }
        return { criticalStocksCount: criticalStocks.length };
    }
    catch (error) {
        firebase_functions_1.logger.error("Erreur lors de la vérification des stocks critiques", { error });
        throw error;
    }
});
