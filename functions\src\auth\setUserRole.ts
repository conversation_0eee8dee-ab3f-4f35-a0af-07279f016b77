import * as functions from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { HttpsError } from "firebase-functions/v1/https";

/**
 * Cloud Function callable permettant à un administrateur
 * d’assigner un rôle à un utilisateur identifié par UID **ou** e-mail.
 */
export const setUserRole = functions.https.onCall(async (data, context) => {
  /* 1. Vérifie que l’appelant est admin  */
  if (!context.auth || context.auth.token.role !== "admin") {
    throw new HttpsError("permission-denied", "Seul un admin peut modifier un rôle.");
  }

  /* 2. Validation et extraction des paramètres */
  const ALLOWED = new Set(["admin", "regisseur", "utilisateur"]);

  const role = String(data?.role || "").trim();
  if (!ALLOWED.has(role)) {
    throw new HttpsError("invalid-argument", "Paramètre 'role' invalide.");
  }

  const userId = String(data?.userId || "").trim();
  const email = String(data?.email || "").trim();

  let uid = userId;
  if (!uid) {
    if (!email) {
      throw new HttpsError("invalid-argument", "Paramètre 'userId' ou 'email' requis.");
    }
    const userRec = await admin.auth().getUserByEmail(email);
    uid = userRec.uid;
  }

  /* 3. Mise à jour des claims */
  await admin.auth().getUser(uid); // visible par les tests
  await admin.auth().setCustomUserClaims(uid, { role });

  return { success: true, newRole: role };
});
