"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testDashboardAlerts = exports.getDashboardMetrics = exports.setupDashboardMonitoring = exports.sendOverdueReminders = exports.getOverdueEmprunts = exports.checkCriticalStockLevels = exports.getStockAlerts = exports.getDashboardData = exports.updateEmpruntStatus = exports.generateEmpruntLabels = exports.createEmprunt = exports.setUserRole = exports.onUserCreate = void 0;
const app_1 = require("firebase-admin/app");
if (!(0, app_1.getApps)().length) {
    (0, app_1.initializeApp)(); // credentials auto-gérées par l’émulateur
}
/* ── Exports des Cloud Functions ── */
var onUserCreate_1 = require("./auth/onUserCreate");
Object.defineProperty(exports, "onUserCreate", { enumerable: true, get: function () { return onUserCreate_1.onUserCreate; } });
var setUserRole_1 = require("./auth/setUserRole");
Object.defineProperty(exports, "setUserRole", { enumerable: true, get: function () { return setUserRole_1.setUserRole; } });
/* ── Exports des fonctions Emprunts ── */
var createEmprunt_1 = require("./emprunts/createEmprunt");
Object.defineProperty(exports, "createEmprunt", { enumerable: true, get: function () { return createEmprunt_1.createEmprunt; } });
var generateEmpruntLabels_1 = require("./emprunts/generateEmpruntLabels");
Object.defineProperty(exports, "generateEmpruntLabels", { enumerable: true, get: function () { return generateEmpruntLabels_1.generateEmpruntLabels; } });
var updateEmpruntStatus_1 = require("./emprunts/updateEmpruntStatus");
Object.defineProperty(exports, "updateEmpruntStatus", { enumerable: true, get: function () { return updateEmpruntStatus_1.updateEmpruntStatus; } });
/* ── Exports des fonctions Dashboard ── */
var getDashboardData_1 = require("./dashboard/getDashboardData");
Object.defineProperty(exports, "getDashboardData", { enumerable: true, get: function () { return getDashboardData_1.getDashboardData; } });
var getStockAlerts_1 = require("./dashboard/getStockAlerts");
Object.defineProperty(exports, "getStockAlerts", { enumerable: true, get: function () { return getStockAlerts_1.getStockAlerts; } });
Object.defineProperty(exports, "checkCriticalStockLevels", { enumerable: true, get: function () { return getStockAlerts_1.checkCriticalStockLevels; } });
var getOverdueEmprunts_1 = require("./dashboard/getOverdueEmprunts");
Object.defineProperty(exports, "getOverdueEmprunts", { enumerable: true, get: function () { return getOverdueEmprunts_1.getOverdueEmprunts; } });
Object.defineProperty(exports, "sendOverdueReminders", { enumerable: true, get: function () { return getOverdueEmprunts_1.sendOverdueReminders; } });
var setupMonitoring_1 = require("./dashboard/setupMonitoring");
Object.defineProperty(exports, "setupDashboardMonitoring", { enumerable: true, get: function () { return setupMonitoring_1.setupDashboardMonitoring; } });
Object.defineProperty(exports, "getDashboardMetrics", { enumerable: true, get: function () { return setupMonitoring_1.getDashboardMetrics; } });
Object.defineProperty(exports, "testDashboardAlerts", { enumerable: true, get: function () { return setupMonitoring_1.testDashboardAlerts; } });
