/**
 * Configuration Firebase pour SIGMA
 * Ce fichier contient la configuration Firebase et l'initialisation des services
 */

// Configuration Firebase - À remplacer par les vraies valeurs du projet sigma-nova
const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "sigma-nova.firebaseapp.com",
  projectId: "sigma-nova",
  storageBucket: "sigma-nova.appspot.com",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
  appId: "YOUR_APP_ID"
};

// Initialisation Firebase
firebase.initializeApp(firebaseConfig);

// Services Firebase
const auth = firebase.auth();
const db = firebase.firestore();
const functions = firebase.functions();
const storage = firebase.storage();

// Configuration pour les émulateurs en développement
if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
  console.log('🔧 Mode développement - Utilisation des émulateurs Firebase');
  
  // Émulateur Auth
  auth.useEmulator('http://localhost:9099');
  
  // Émulateur Firestore
  db.useEmulator('localhost', 8080);
  
  // Émulateur Functions
  functions.useEmulator('localhost', 5001);
  
  // Émulateur Storage
  storage.useEmulator('localhost', 9199);
}

// Configuration du provider Google
const googleProvider = new firebase.auth.GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');

// Export des services pour utilisation dans d'autres fichiers
window.firebaseServices = {
  auth,
  db,
  functions,
  storage,
  googleProvider
};

console.log('✅ Firebase initialisé avec succès');
