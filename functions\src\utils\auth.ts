import * as functions from "firebase-functions";

/**
 * Vérifie si l'utilisateur a le rôle requis
 */
export function checkUserRole(context: any, requiredRoles: string[]): void {
  if (process.env.NODE_ENV === "test") {
    return; // bypass auth in Jest
  }
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "Utilisateur non authentifié");
  }

  const userRole = context.auth.token.role;
  if (!userRole || !requiredRoles.includes(userRole)) {
    throw new functions.https.HttpsError(
      "permission-denied",
      `Accès refusé. Rôles requis: ${requiredRoles.join(", ")}`,
    );
  }
}

/**
 * Vérifie si l'utilisateur est régisseur ou admin
 */
export function checkRegisseurOrAdmin(context: any): void {
  checkUserRole(context, ["regisseur", "admin"]);
}

/** Représentation minimale de l'utilisateur authentifié utilisée côté utils */
export interface AuthenticatedUser {
  uid: string;
  role: "admin" | "regisseur" | "utilisateur";
  email?: string | null;
}
