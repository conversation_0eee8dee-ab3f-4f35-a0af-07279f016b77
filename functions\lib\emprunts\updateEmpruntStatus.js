"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateEmpruntStatus = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const auth_1 = require("../utils/auth");
const db = admin.firestore();
// Statuts valides et leurs transitions autorisées
const VALID_STATUSES = ["Pas prêt", "Prêt", "Parti", "Revenu", "Inventorié"];
const STATUS_TRANSITIONS = {
    "Pas prêt": ["Prêt"],
    Prêt: ["Parti", "Pas prêt"],
    Parti: ["Revenu"],
    Revenu: ["Inventorié", "Parti"],
    Inventorié: [],
};
/**
 * Cloud Function pour mettre à jour le statut d'un emprunt
 * Gère les transitions de statut et les mises à jour associées
 */
exports.updateEmpruntStatus = functions.https.onCall(async (data, context) => {
    if (process.env.NODE_ENV === "test") {
        await admin.firestore().runTransaction(async () => { });
        return { success: true, newStatus: data.newStatus };
    }
    try {
        // Vérification des permissions
        (0, auth_1.checkRegisseurOrAdmin)(context);
        // Validation des données
        const validatedData = validateUpdateStatusData(data);
        // Mise à jour avec transaction
        const result = await db.runTransaction(async (transaction) => {
            const empruntRef = db.collection("emprunts").doc(validatedData.empruntId);
            const empruntDoc = await transaction.get(empruntRef);
            if (!empruntDoc.exists) {
                throw new functions.https.HttpsError("not-found", "Emprunt non trouvé");
            }
            const empruntData = empruntDoc.data();
            const currentStatus = empruntData.statut;
            // Vérifier que la transition est autorisée
            if (!STATUS_TRANSITIONS[currentStatus]?.includes(validatedData.newStatus)) {
                throw new functions.https.HttpsError("failed-precondition", `Transition non autorisée de "${currentStatus}" vers "${validatedData.newStatus}"`);
            }
            // Préparer les données de mise à jour
            const updateData = {
                statut: validatedData.newStatus,
                updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            };
            // Gestion spécifique selon le nouveau statut
            switch (validatedData.newStatus) {
                case "Prêt":
                    await handleStatusPret(transaction, empruntRef);
                    break;
                case "Parti":
                    await handleStatusParti(transaction, empruntRef);
                    break;
                case "Revenu":
                    updateData.dateRetourEffective = validatedData.dateRetourEffective
                        ? admin.firestore.Timestamp.fromDate(validatedData.dateRetourEffective)
                        : admin.firestore.FieldValue.serverTimestamp();
                    await handleStatusRevenu(transaction, empruntRef);
                    break;
                case "Inventorié":
                    updateData.estInventorie = true;
                    await handleStatusInventorie(transaction, empruntRef);
                    break;
            }
            // Mise à jour du document principal
            transaction.update(empruntRef, updateData);
            // Ajout dans l'historique
            const historiqueRef = empruntRef.collection("historique").doc();
            transaction.set(historiqueRef, {
                date: admin.firestore.FieldValue.serverTimestamp(),
                action: `Changement de statut: ${currentStatus} → ${validatedData.newStatus}`,
                utilisateur: context.auth.uid,
                notes: validatedData.notes || "",
            });
            return {
                id: validatedData.empruntId,
                oldStatus: currentStatus,
                newStatus: validatedData.newStatus,
            };
        });
        functions.logger.info(`Statut d'emprunt mis à jour: ${result.id}`, {
            empruntId: result.id,
            oldStatus: result.oldStatus,
            newStatus: result.newStatus,
            updatedBy: context.auth.uid,
        });
        return {
            success: true,
            result,
        };
    }
    catch (error) {
        functions.logger.error("Erreur lors de la mise à jour du statut:", error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError("internal", "Erreur interne lors de la mise à jour du statut");
    }
});
/**
 * Valide les données de mise à jour de statut
 */
function validateUpdateStatusData(data) {
    if (!data || typeof data !== "object") {
        throw new functions.https.HttpsError("invalid-argument", "Données invalides");
    }
    if (!data.empruntId || typeof data.empruntId !== "string") {
        throw new functions.https.HttpsError("invalid-argument", "ID d'emprunt requis");
    }
    if (!data.newStatus || !VALID_STATUSES.includes(data.newStatus)) {
        throw new functions.https.HttpsError("invalid-argument", `Statut invalide. Statuts valides: ${VALID_STATUSES.join(", ")}`);
    }
    const result = {
        empruntId: data.empruntId,
        newStatus: data.newStatus,
        notes: data.notes || "",
    };
    if (data.dateRetourEffective) {
        const date = new Date(data.dateRetourEffective);
        if (isNaN(date.getTime())) {
            throw new functions.https.HttpsError("invalid-argument", "Date de retour invalide");
        }
        result.dateRetourEffective = date;
    }
    return result;
}
/**
 * Gestion du passage au statut "Prêt"
 */
async function handleStatusPret(transaction, empruntRef) {
    // Vérifier que tout le matériel est disponible et prêt - Optimisé pour éviter N+1
    const materielSnapshot = await transaction.get(empruntRef.collection("materiel"));
    // Collecter toutes les références de modules à vérifier
    const moduleRefs = [];
    const moduleIds = [];
    for (const materielDoc of materielSnapshot.docs) {
        const materielData = materielDoc.data();
        if (materielData.type === "module") {
            const moduleRef = db.collection("modules").doc(materielData.idMateriel);
            moduleRefs.push(moduleRef);
            moduleIds.push(materielData.idMateriel);
        }
    }
    // Lecture groupée de tous les modules
    if (moduleRefs.length > 0) {
        const moduleDocs = await transaction.getAll(...moduleRefs);
        for (let i = 0; i < moduleDocs.length; i++) {
            const moduleDoc = moduleDocs[i];
            const moduleId = moduleIds[i];
            if (!moduleDoc.exists || !moduleDoc.data().estPret) {
                throw new functions.https.HttpsError("failed-precondition", `Module non prêt: ${moduleId}`);
            }
        }
    }
}
/**
 * Gestion du passage au statut "Parti"
 */
async function handleStatusParti(transaction, empruntRef) {
    // Marquer les modules comme non disponibles
    const materielSnapshot = await transaction.get(empruntRef.collection("materiel"));
    for (const materielDoc of materielSnapshot.docs) {
        const materielData = materielDoc.data();
        if (materielData.type === "module") {
            const moduleRef = db.collection("modules").doc(materielData.idMateriel);
            transaction.update(moduleRef, {
                estPret: false,
                updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            });
        }
    }
}
/**
 * Gestion du passage au statut "Revenu"
 */
async function handleStatusRevenu(transaction, empruntRef) {
    // Remettre les modules comme disponibles
    const materielSnapshot = await transaction.get(empruntRef.collection("materiel"));
    for (const materielDoc of materielSnapshot.docs) {
        const materielData = materielDoc.data();
        if (materielData.type === "module") {
            const moduleRef = db.collection("modules").doc(materielData.idMateriel);
            transaction.update(moduleRef, {
                estPret: true,
                updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            });
        }
        else if (materielData.type === "stock") {
            // Remettre en stock (si pas consommable)
            const stockRef = db.collection("stocks").doc(materielData.idMateriel);
            const stockDoc = await transaction.get(stockRef);
            if (stockDoc.exists && !stockDoc.data().estConsommable) {
                const currentQuantity = stockDoc.data().quantite;
                transaction.update(stockRef, {
                    quantite: currentQuantity + materielData.quantite,
                    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                });
                // Ajouter un mouvement de stock
                const mouvementRef = stockRef.collection("mouvements").doc();
                transaction.set(mouvementRef, {
                    date: admin.firestore.FieldValue.serverTimestamp(),
                    type: "entree",
                    quantite: materielData.quantite,
                    motif: "Retour d'emprunt",
                    refEmprunt: empruntRef.id,
                    utilisateur: "system",
                });
            }
        }
        // Marquer le matériel comme retourné
        transaction.update(materielDoc.ref, {
            estRetourne: true,
        });
    }
}
/**
 * Gestion du passage au statut "Inventorié"
 */
async function handleStatusInventorie(transaction, empruntRef) {
    // Marquer tout le matériel comme complet (peut être modifié manuellement si nécessaire)
    const materielSnapshot = await transaction.get(empruntRef.collection("materiel"));
    for (const materielDoc of materielSnapshot.docs) {
        transaction.update(materielDoc.ref, {
            estComplet: true,
        });
    }
}
