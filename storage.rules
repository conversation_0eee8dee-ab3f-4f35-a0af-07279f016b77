rules_version = '2';

// Firebase Storage Security Rules pour SIGMA
// Basées sur les rôles utilisateurs (Custom Claims) et les permissions d'emprunt

service firebase.storage {
  match /b/{bucket}/o {

    // --- Fonctions Utilitaires ---
    
    // Vérifier si l'utilisateur est authentifié
    function isAuthenticated() {
      return request.auth != null;
    }

    // Vérifier le rôle de l'utilisateur
    function hasRole(role) {
      return isAuthenticated() && (request.auth.token.role == role || request.auth.token.role == 'admin');
    }

    function isAdmin() {
      return isAuthenticated() && request.auth.token.role == 'admin';
    }

    function isRegisseur() {
      return hasRole('regisseur');
    }

    function isUtilisateur() {
      return isAuthenticated() && request.auth.token.role in ['utilisateur', 'regisseur', 'admin'];
    }

    // Vérifier si l'utilisateur est propriétaire du fichier
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Vérifier si le fichier est une image autorisée
    function isValidImageType() {
      return request.resource.contentType.matches('image/(jpeg|jpg|png|gif|webp)');
    }

    // Vérifier si le fichier est un PDF autorisé
    function isValidPdfType() {
      return request.resource.contentType == 'application/pdf';
    }

    // Vérifier la taille du fichier (max 10MB)
    function isValidFileSize() {
      return request.resource.size <= 10 * 1024 * 1024;
    }

    // --- RÈGLES PAR DOSSIER ---

    // Dossier des avatars utilisateurs
    // Structure : /avatars/{userId}/{fileName}
    match /avatars/{userId}/{fileName} {
      // Lecture : propriétaire ou admin
      allow read: if isOwner(userId) || isAdmin();
      
      // Écriture : propriétaire uniquement avec validation
      allow write: if isOwner(userId) && 
                     isValidImageType() && 
                     isValidFileSize();
      
      // Suppression : propriétaire ou admin
      allow delete: if isOwner(userId) || isAdmin();
    }

    // Dossier des documents d'emprunts
    // Structure : /emprunts/{empruntId}/documents/{fileName}
    match /emprunts/{empruntId}/documents/{fileName} {
      // Lecture : tous les utilisateurs authentifiés (pour voir les docs d'emprunt)
      allow read: if isUtilisateur();
      
      // Écriture : admin et régisseur uniquement
      allow write: if isRegisseur() && 
                     (isValidPdfType() || isValidImageType()) && 
                     isValidFileSize();
      
      // Suppression : admin et régisseur uniquement
      allow delete: if isRegisseur();
    }

    // Dossier des preuves de livraison
    // Structure : /livraisons/{livraisonId}/preuves/{fileName}
    match /livraisons/{livraisonId}/preuves/{fileName} {
      // Lecture : tous les utilisateurs authentifiés
      allow read: if isUtilisateur();
      
      // Écriture : admin et régisseur uniquement (photos de livraison)
      allow write: if isRegisseur() && 
                     isValidImageType() && 
                     isValidFileSize();
      
      // Suppression : admin et régisseur uniquement
      allow delete: if isRegisseur();
    }

    // Dossier des images de modules
    // Structure : /modules/{moduleId}/images/{fileName}
    match /modules/{moduleId}/images/{fileName} {
      // Lecture : tous les utilisateurs authentifiés (pour voir les modules)
      allow read: if isUtilisateur();
      
      // Écriture : admin et régisseur uniquement
      allow write: if isRegisseur() && 
                     isValidImageType() && 
                     isValidFileSize();
      
      // Suppression : admin et régisseur uniquement
      allow delete: if isRegisseur();
    }

    // Dossier des images de stock
    // Structure : /stocks/{stockId}/images/{fileName}
    match /stocks/{stockId}/images/{fileName} {
      // Lecture : admin et régisseur uniquement
      allow read: if isRegisseur();
      
      // Écriture : admin et régisseur uniquement
      allow write: if isRegisseur() && 
                     isValidImageType() && 
                     isValidFileSize();
      
      // Suppression : admin et régisseur uniquement
      allow delete: if isRegisseur();
    }

    // Dossier des documents administratifs
    // Structure : /admin/documents/{fileName}
    match /admin/documents/{fileName} {
      // Lecture : admin uniquement
      allow read: if isAdmin();
      
      // Écriture : admin uniquement
      allow write: if isAdmin() && 
                     (isValidPdfType() || isValidImageType()) && 
                     isValidFileSize();
      
      // Suppression : admin uniquement
      allow delete: if isAdmin();
    }

    // Dossier des exports et rapports
    // Structure : /exports/{type}/{fileName}
    match /exports/{type}/{fileName} {
      // Lecture : admin et régisseur uniquement
      allow read: if isRegisseur();
      
      // Écriture : admin et régisseur uniquement (génération de rapports)
      allow write: if isRegisseur() && 
                     (isValidPdfType() || request.resource.contentType.matches('text/(csv|plain)') || 
                      request.resource.contentType.matches('application/(json|vnd.ms-excel|vnd.openxmlformats-officedocument.spreadsheetml.sheet)')) && 
                     isValidFileSize();
      
      // Suppression : admin et régisseur uniquement
      allow delete: if isRegisseur();
    }

    // Dossier des sauvegardes
    // Structure : /backups/{date}/{fileName}
    match /backups/{date}/{fileName} {
      // Lecture : admin uniquement
      allow read: if isAdmin();
      
      // Écriture : admin uniquement (sauvegardes automatiques)
      allow write: if isAdmin() && 
                     (request.resource.contentType.matches('application/(json|zip|gzip)') || 
                      request.resource.contentType.matches('text/plain')) && 
                     request.resource.size <= 100 * 1024 * 1024; // 100MB max pour les sauvegardes
      
      // Suppression : admin uniquement
      allow delete: if isAdmin();
    }

    // Dossier temporaire pour uploads
    // Structure : /temp/{userId}/{fileName}
    match /temp/{userId}/{fileName} {
      // Lecture : propriétaire uniquement
      allow read: if isOwner(userId);
      
      // Écriture : propriétaire uniquement avec validation stricte
      allow write: if isOwner(userId) && 
                     (isValidImageType() || isValidPdfType()) && 
                     isValidFileSize();
      
      // Suppression : propriétaire ou admin (nettoyage)
      allow delete: if isOwner(userId) || isAdmin();
    }

    // Dossier public (logos, assets)
    // Structure : /public/{fileName}
    match /public/{fileName} {
      // Lecture : tous les utilisateurs authentifiés
      allow read: if isUtilisateur();
      
      // Écriture : admin uniquement
      allow write: if isAdmin() && 
                     isValidImageType() && 
                     isValidFileSize();
      
      // Suppression : admin uniquement
      allow delete: if isAdmin();
    }

    // Règle de sécurité par défaut : tout refuser
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
