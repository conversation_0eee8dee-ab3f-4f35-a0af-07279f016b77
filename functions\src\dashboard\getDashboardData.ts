/**
 * Cloud Function pour récupérer les données du dashboard SIGMA
 * Agrégation optimisée des 7 tableaux temps-réel
 */

import * as functions from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { checkRegisseurOrAdmin } from "../utils/auth";
import { logger } from "firebase-functions";
import {
  sendDashboardQueryDuration,
  sendFirestoreReadRate,
  withMonitoring
} from "../utils/monitoring";

const db = admin.firestore();

/**
 * Interface pour les données du dashboard
 */
interface DashboardData {
  stockAlerts: any[];
  missingMaterial: any[];
  overdueEmprunts: any[];
  upcomingEmprunts: any[];
  nonOpModules: any[];
  nonOpMaterial: any[];
  pendingEmprunts: any[];
  timestamp: string;
  performance: {
    totalQueries: number;
    executionTimeMs: number;
  };
}

/**
 * Récupérer les alertes stock (quantité <= seuil)
 */
async function getStockAlerts(): Promise<any[]> {
  try {
    const snapshot = await db
      .collection("stocks")
      .where("estOperationnel", "==", true)
      .orderBy("quantite", "asc")
      .limit(20)
      .get();

    return snapshot.docs
      .map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }))
      .filter((stock: any) => stock.quantite <= stock.seuilAlerte);
  } catch (error) {
    logger.error("Erreur lors de la récupération des alertes stock", { error });
    return [];
  }
}

/**
 * Récupérer le matériel manquant (à commander)
 */
async function getMissingMaterial(): Promise<any[]> {
  try {
    const snapshot = await db
      .collection("stocks")
      .where("aCommander", "==", true)
      .orderBy("updatedAt", "desc")
      .limit(20)
      .get();

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    logger.error("Erreur lors de la récupération du matériel manquant", { error });
    return [];
  }
}

/**
 * Récupérer les emprunts en retard
 */
async function getOverdueEmprunts(): Promise<any[]> {
  try {
    const now = new Date();
    const snapshot = await db
      .collection("emprunts")
      .where("statut", "==", "Parti")
      .where("dateRetourPrevue", "<", now)
      .orderBy("dateRetourPrevue", "asc")
      .limit(20)
      .get();

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    logger.error("Erreur lors de la récupération des emprunts en retard", { error });
    return [];
  }
}

/**
 * Récupérer les prochains emprunts (< 30 jours)
 */
async function getUpcomingEmprunts(): Promise<any[]> {
  try {
    const now = new Date();
    const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    const snapshot = await db
      .collection("emprunts")
      .where("statut", "in", ["Pas prêt", "Prêt"])
      .where("dateDepart", ">=", now)
      .where("dateDepart", "<=", in30Days)
      .orderBy("dateDepart", "asc")
      .limit(20)
      .get();

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    logger.error("Erreur lors de la récupération des prochains emprunts", { error });
    return [];
  }
}

/**
 * Récupérer les modules non opérationnels
 */
async function getNonOperationalModules(): Promise<any[]> {
  try {
    const snapshot = await db
      .collection("modules")
      .where("estPret", "==", false)
      .orderBy("updatedAt", "desc")
      .limit(20)
      .get();

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    logger.error("Erreur lors de la récupération des modules non opérationnels", { error });
    return [];
  }
}

/**
 * Récupérer le matériel non opérationnel
 */
async function getNonOperationalMaterial(): Promise<any[]> {
  try {
    const snapshot = await db
      .collection("stocks")
      .where("estOperationnel", "==", false)
      .orderBy("updatedAt", "desc")
      .limit(20)
      .get();

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    logger.error("Erreur lors de la récupération du matériel non opérationnel", { error });
    return [];
  }
}

/**
 * Récupérer les emprunts en attente (non inventoriés/facturés)
 */
async function getPendingEmprunts(): Promise<any[]> {
  try {
    // Récupérer les emprunts non inventoriés
    const nonInventoriedSnapshot = await db
      .collection("emprunts")
      .where("statut", "==", "Revenu")
      .where("estInventorie", "==", false)
      .orderBy("dateRetourEffective", "desc")
      .limit(10)
      .get();

    // Récupérer les emprunts non facturés
    const nonBilledSnapshot = await db
      .collection("emprunts")
      .where("statut", "==", "Revenu")
      .where("estFacture", "==", false)
      .orderBy("dateRetourEffective", "desc")
      .limit(10)
      .get();

    const nonInventoried = nonInventoriedSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      pendingType: "inventaire",
    }));

    const nonBilled = nonBilledSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      pendingType: "facturation",
    }));

    // Fusionner et dédupliquer
    const allPending = [...nonInventoried, ...nonBilled];
    const uniquePending = allPending.filter(
      (emprunt, index, self) => index === self.findIndex((e) => e.id === emprunt.id),
    );

    return uniquePending.slice(0, 20);
  } catch (error) {
    logger.error("Erreur lors de la récupération des emprunts en attente", { error });
    return [];
  }
}

/**
 * Cloud Function principale pour récupérer toutes les données du dashboard
 */
export const getDashboardData = functions
  .region("europe-west1")
  .https.onCall(withMonitoring('getDashboardData', async (data, context) => {
    // Bypass pour les tests
    if (process.env.NODE_ENV === "test") {
      return {
        stockAlerts: [],
        missingMaterial: [],
        overdueEmprunts: [],
        upcomingEmprunts: [],
        nonOpModules: [],
        nonOpMaterial: [],
        pendingEmprunts: [],
        timestamp: new Date().toISOString(),
        performance: { totalQueries: 7, executionTimeMs: 50 },
      };
    }

    const startTime = Date.now();

    try {
      // Vérification des permissions
      checkRegisseurOrAdmin(context);

      logger.info("Récupération des données dashboard", {
        userId: context.auth?.uid,
        userRole: context.auth?.token?.role,
      });

      // Exécution parallèle de toutes les requêtes
      const [
        stockAlerts,
        missingMaterial,
        overdueEmprunts,
        upcomingEmprunts,
        nonOpModules,
        nonOpMaterial,
        pendingEmprunts,
      ] = await Promise.all([
        getStockAlerts(),
        getMissingMaterial(),
        getOverdueEmprunts(),
        getUpcomingEmprunts(),
        getNonOperationalModules(),
        getNonOperationalMaterial(),
        getPendingEmprunts(),
      ]);

      const executionTime = Date.now() - startTime;

      // Calculer le nombre total de lectures Firestore (estimation)
      const totalReads = stockAlerts.length + missingMaterial.length + overdueEmprunts.length +
                        upcomingEmprunts.length + nonOpModules.length + nonOpMaterial.length +
                        pendingEmprunts.length;

      // Envoyer les métriques de monitoring
      await sendFirestoreReadRate(totalReads / (executionTime / 1000)); // lectures/seconde

      const result: DashboardData = {
        stockAlerts,
        missingMaterial,
        overdueEmprunts,
        upcomingEmprunts,
        nonOpModules,
        nonOpMaterial,
        pendingEmprunts,
        timestamp: new Date().toISOString(),
        performance: {
          totalQueries: 7,
          executionTimeMs: executionTime,
        },
      };

      logger.info("Dashboard data récupérées avec succès", {
        userId: context.auth?.uid,
        executionTimeMs: executionTime,
        totalItems: {
          stockAlerts: stockAlerts.length,
          missingMaterial: missingMaterial.length,
          overdueEmprunts: overdueEmprunts.length,
          upcomingEmprunts: upcomingEmprunts.length,
          nonOpModules: nonOpModules.length,
          nonOpMaterial: nonOpMaterial.length,
          pendingEmprunts: pendingEmprunts.length,
        },
      });

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error("Erreur lors de la récupération des données dashboard", {
        userId: context.auth?.uid,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        executionTimeMs: executionTime,
      });

      throw new functions.https.HttpsError(
        "internal",
        "Erreur lors de la récupération des données dashboard",
      );
    }
  }));
