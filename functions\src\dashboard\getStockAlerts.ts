/**
 * Cloud Function spécialisée pour les alertes stock critiques
 * Optimisée pour les notifications et le monitoring
 */

import * as functions from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { checkRegisseurOrAdmin } from "../utils/auth";
import { logger } from "firebase-functions";
import { sendCriticalStockCount, withMonitoring } from "../utils/monitoring";

const db = admin.firestore();

/**
 * Interface pour une alerte stock
 */
interface StockAlert {
  id: string;
  nom: string;
  quantite: number;
  seuilAlerte: number;
  localisation: string;
  categorie: string;
  severity: "critical" | "warning" | "info";
  percentageRemaining: number;
  daysUntilEmpty?: number;
}

/**
 * Interface pour les paramètres de la requête
 */
interface GetStockAlertsParams {
  severity?: "critical" | "warning" | "info";
  limit?: number;
  includeProjections?: boolean;
}

/**
 * Calculer la sévérité d'une alerte stock
 */
function calculateSeverity(quantite: number, seuilAlerte: number): "critical" | "warning" | "info" {
  const percentage = (quantite / seuilAlerte) * 100;
  
  if (percentage <= 25) return "critical";  // 25% ou moins du seuil
  if (percentage <= 50) return "warning";   // 50% ou moins du seuil
  return "info";                            // Au-dessus de 50% du seuil
}

/**
 * Estimer les jours avant épuisement (basé sur consommation moyenne)
 */
async function estimateDaysUntilEmpty(stockId: string, currentQuantity: number): Promise<number | undefined> {
  try {
    // Récupérer les mouvements des 30 derniers jours
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    const movementsSnapshot = await db
      .collection("stocks")
      .doc(stockId)
      .collection("mouvements")
      .where("type", "==", "sortie")
      .where("date", ">=", thirtyDaysAgo)
      .get();

    if (movementsSnapshot.empty) return undefined;

    // Calculer la consommation moyenne par jour
    const totalConsumption = movementsSnapshot.docs.reduce(
      (sum, doc) => sum + (doc.data().quantite || 0),
      0
    );
    
    const averageDailyConsumption = totalConsumption / 30;
    
    if (averageDailyConsumption <= 0) return undefined;
    
    return Math.floor(currentQuantity / averageDailyConsumption);
  } catch (error) {
    logger.warn("Impossible de calculer les jours avant épuisement", { stockId, error });
    return undefined;
  }
}

/**
 * Cloud Function pour récupérer les alertes stock avec détails avancés
 */
export const getStockAlerts = functions
  .region("europe-west1")
  .https.onCall(withMonitoring('getStockAlerts', async (data: GetStockAlertsParams, context) => {
    // Bypass pour les tests
    if (process.env.NODE_ENV === "test") {
      return {
        alerts: [],
        summary: {
          total: 0,
          critical: 0,
          warning: 0,
          info: 0
        },
        timestamp: new Date().toISOString()
      };
    }

    const startTime = Date.now();

    try {
      // Vérification des permissions
      checkRegisseurOrAdmin(context);

      const { severity, limit = 50, includeProjections = false } = data || {};

      logger.info("Récupération des alertes stock", {
        userId: context.auth?.uid,
        severity,
        limit,
        includeProjections
      });

      // Requête optimisée pour les stocks critiques
      let query = db
        .collection("stocks")
        .where("estOperationnel", "==", true)
        .orderBy("quantite", "asc")
        .limit(limit);

      const snapshot = await query.get();

      // Traitement des alertes avec calculs avancés
      const alertsPromises = snapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        .filter((stock: any) => stock.quantite <= stock.seuilAlerte)
        .map(async (stock: any): Promise<StockAlert> => {
          const severity = calculateSeverity(stock.quantite, stock.seuilAlerte);
          const percentageRemaining = (stock.quantite / stock.seuilAlerte) * 100;
          
          let daysUntilEmpty: number | undefined;
          if (includeProjections) {
            daysUntilEmpty = await estimateDaysUntilEmpty(stock.id, stock.quantite);
          }

          return {
            id: stock.id,
            nom: stock.nom,
            quantite: stock.quantite,
            seuilAlerte: stock.seuilAlerte,
            localisation: stock.localisation || "Non spécifiée",
            categorie: stock.categorie || "Général",
            severity,
            percentageRemaining: Math.round(percentageRemaining),
            daysUntilEmpty
          };
        });

      const alerts = await Promise.all(alertsPromises);

      // Filtrer par sévérité si spécifiée
      const filteredAlerts = severity 
        ? alerts.filter(alert => alert.severity === severity)
        : alerts;

      // Calculer le résumé
      const summary = {
        total: alerts.length,
        critical: alerts.filter(a => a.severity === "critical").length,
        warning: alerts.filter(a => a.severity === "warning").length,
        info: alerts.filter(a => a.severity === "info").length
      };

      const executionTime = Date.now() - startTime;

      // Envoyer le nombre d'alertes critiques au monitoring
      await sendCriticalStockCount(summary.critical);

      logger.info("Alertes stock récupérées avec succès", {
        userId: context.auth?.uid,
        totalAlerts: alerts.length,
        filteredAlerts: filteredAlerts.length,
        summary,
        executionTimeMs: executionTime
      });

      return {
        alerts: filteredAlerts,
        summary,
        timestamp: new Date().toISOString(),
        performance: {
          executionTimeMs: executionTime,
          includeProjections
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error("Erreur lors de la récupération des alertes stock", {
        userId: context.auth?.uid,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        executionTimeMs: executionTime
      });

      throw new functions.https.HttpsError(
        "internal",
        "Erreur lors de la récupération des alertes stock"
      );
    }
  }));

/**
 * Cloud Function pour vérifier les seuils critiques (pour monitoring automatique)
 */
export const checkCriticalStockLevels = functions
  .region("europe-west1")
  .pubsub.schedule("every 1 hours")
  .onRun(async (context) => {
    try {
      logger.info("Vérification automatique des niveaux de stock critiques");

      // Récupérer tous les stocks critiques (quantité <= 25% du seuil)
      const snapshot = await db
        .collection("stocks")
        .where("estOperationnel", "==", true)
        .get();

      const criticalStocks = snapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter((stock: any) => {
          const percentage = (stock.quantite / stock.seuilAlerte) * 100;
          return percentage <= 25; // Critique si <= 25% du seuil
        });

      if (criticalStocks.length > 0) {
        logger.warn("Stocks critiques détectés", {
          count: criticalStocks.length,
          stocks: criticalStocks.map((s: any) => ({
            id: s.id,
            nom: s.nom,
            quantite: s.quantite,
            seuil: s.seuilAlerte
          }))
        });

        // Ici, on pourrait envoyer des notifications push, emails, etc.
        // await sendCriticalStockNotifications(criticalStocks);
      }

      return { criticalStocksCount: criticalStocks.length };

    } catch (error) {
      logger.error("Erreur lors de la vérification des stocks critiques", { error });
      throw error;
    }
  });
