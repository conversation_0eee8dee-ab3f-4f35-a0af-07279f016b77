/**
 * Styles CSS pour le Dashboard SIGMA
 * Design moderne et responsive avec thème sombre/clair
 */

/* Variables CSS pour thème cohérent */
:root {
  /* Couleurs principales */
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;
  
  /* Couleurs de fond */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  --bg-dark: #1e293b;
  
  /* Couleurs de texte */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-white: #ffffff;
  
  /* Ombres */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  
  /* Bordures */
  --border-radius: 8px;
  --border-color: #e2e8f0;
  
  /* Espacements */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
}

/* Reset et base */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header principal */
.dashboard-header {
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  color: var(--text-white);
  padding: var(--spacing-md) var(--spacing-xl);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.refresh-indicator {
  font-size: 0.875rem;
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.refresh-indicator.active {
  color: var(--success-color);
}

.refresh-indicator.updated {
  color: var(--warning-color);
  animation: pulse 0.5s ease-in-out;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.performance-metrics {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.875rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Container principal */
.dashboard-container {
  flex: 1;
  padding: var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Grille des tableaux */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* Cartes dashboard */
.dashboard-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-left: 4px solid var(--info-color);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card.critical {
  border-left-color: var(--error-color);
}

.dashboard-card.warning {
  border-left-color: var(--warning-color);
}

.dashboard-card.info {
  border-left-color: var(--info-color);
}

/* En-têtes des cartes */
.card-header {
  background: var(--bg-secondary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Conteneurs de tableaux */
.table-container {
  max-height: 400px;
  overflow-y: auto;
  position: relative;
}

.table-responsive {
  overflow-x: auto;
}

/* Tableaux */
.dashboard-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.dashboard-table th {
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.dashboard-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  vertical-align: top;
}

.dashboard-table tbody tr:hover {
  background: var(--bg-secondary);
}

/* Lignes spécialisées */
.alert-row.critical {
  background: rgba(239, 68, 68, 0.05);
  border-left: 3px solid var(--error-color);
}

.alert-row.warning {
  background: rgba(245, 158, 11, 0.05);
  border-left: 3px solid var(--warning-color);
}

.overdue-row.high {
  background: rgba(239, 68, 68, 0.05);
}

.overdue-row.medium {
  background: rgba(245, 158, 11, 0.05);
}

.upcoming-row.urgent {
  background: rgba(239, 68, 68, 0.05);
}

.upcoming-row.soon {
  background: rgba(245, 158, 11, 0.05);
}

/* Badges et indicateurs */
.stock-quantity.critical {
  color: var(--error-color);
  font-weight: 700;
}

.stock-quantity.warning {
  color: var(--warning-color);
  font-weight: 600;
}

.percentage.critical {
  color: var(--error-color);
  font-weight: 700;
}

.percentage.warning {
  color: var(--warning-color);
  font-weight: 600;
}

.overdue-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.overdue-badge.high {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.overdue-badge.medium {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.overdue-badge.low {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.pas-pret {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.status-badge.pret {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.pending-type {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.pending-type.inventaire {
  background: rgba(6, 182, 212, 0.1);
  color: var(--info-color);
}

.pending-type.facturation {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.days-until.urgent {
  color: var(--error-color);
  font-weight: 600;
}

.days-until.soon {
  color: var(--warning-color);
  font-weight: 500;
}

/* Boutons */
.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.btn-primary {
  background: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover {
  background: #1d4ed8;
}

.btn-success {
  background: var(--success-color);
  color: var(--text-white);
}

.btn-success:hover {
  background: #059669;
}

.btn-warning {
  background: var(--warning-color);
  color: var(--text-white);
}

.btn-warning:hover {
  background: #d97706;
}

.btn-info {
  background: var(--info-color);
  color: var(--text-white);
}

.btn-info:hover {
  background: #0891b2;
}

.btn-outline {
  background: transparent;
  color: var(--text-white);
  border: 1px solid currentColor;
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
}

.btn-icon {
  padding: var(--spacing-sm);
  background: transparent;
  border: none;
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: background 0.2s ease;
}

.btn-icon:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* États vides */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

/* Placeholders de chargement */
.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

/* Statistiques des tableaux */
.table-stats {
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.stat-item {
  margin-right: var(--spacing-md);
}

/* Footer */
.dashboard-footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-xl);
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Conteneur d'erreurs */
.error-container {
  margin-bottom: var(--spacing-lg);
}

.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
}

.alert-error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Notifications */
.update-notification,
.refresh-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--success-color);
  color: var(--text-white);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.update-notification.show,
.refresh-notification.show {
  transform: translateX(0);
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--spacing-md);
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
  
  .footer-content {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
  
  .dashboard-table {
    font-size: 0.75rem;
  }
  
  .dashboard-table th,
  .dashboard-table td {
    padding: var(--spacing-sm);
  }
  
  /* Mode mobile pour les tableaux */
  .dashboard-table.mobile-view {
    display: block;
  }
  
  .dashboard-table.mobile-view thead {
    display: none;
  }
  
  .dashboard-table.mobile-view tbody,
  .dashboard-table.mobile-view tr,
  .dashboard-table.mobile-view td {
    display: block;
  }
  
  .dashboard-table.mobile-view tr {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
  }
  
  .dashboard-table.mobile-view td {
    border: none;
    padding: var(--spacing-xs) 0;
  }
  
  .dashboard-table.mobile-view td:before {
    content: attr(data-label) ": ";
    font-weight: 600;
    color: var(--text-secondary);
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: var(--spacing-sm);
  }
  
  .card-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .card-header h3 {
    font-size: 1rem;
  }
  
  .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
  }
}
