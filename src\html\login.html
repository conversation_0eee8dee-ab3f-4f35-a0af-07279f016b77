<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIGMA - Connexion</title>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-functions-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>
    
    <!-- Styles CSS -->
    <link rel="stylesheet" href="../css/login.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Logo et titre -->
            <div class="login-header">
                <div class="logo">
                    <span class="material-icons">inventory_2</span>
                </div>
                <h1>SIGMA</h1>
                <p class="subtitle">Système Informatique de Gestion du Matériel</p>
            </div>

            <!-- Zone de connexion -->
            <div class="login-content">
                <div id="loading-state" class="loading-state" style="display: none;">
                    <div class="spinner"></div>
                    <p>Connexion en cours...</p>
                </div>

                <div id="login-form" class="login-form">
                    <h2>Connexion</h2>
                    <p class="login-description">
                        Connectez-vous avec votre compte Google pour accéder à SIGMA
                    </p>

                    <button id="google-signin-btn" class="google-signin-btn">
                        <svg class="google-icon" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Se connecter avec Google
                    </button>
                </div>

                <!-- Messages d'erreur -->
                <div id="error-message" class="error-message" style="display: none;">
                    <span class="material-icons">error</span>
                    <span id="error-text"></span>
                </div>

                <!-- Messages de succès -->
                <div id="success-message" class="success-message" style="display: none;">
                    <span class="material-icons">check_circle</span>
                    <span id="success-text"></span>
                </div>
            </div>

            <!-- Footer -->
            <div class="login-footer">
                <p>&copy; 2025 SIGMA - Gestion du matériel</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/firebase-config.js"></script>
    <script src="../js/auth.js"></script>
</body>
</html>
