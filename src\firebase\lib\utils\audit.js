"use strict";
/**
 * Module d'audit et de logging pour SIGMA (Corrigé)
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.logAuditEvent = logAuditEvent;
const firestore_1 = require("firebase-admin/firestore");
const firebase_functions_1 = require("firebase-functions");
async function logAuditEvent(user, action, options = {}) {
    var _a;
    try {
        const db = (0, firestore_1.getFirestore)();
        const auditLog = {
            userId: user.uid,
            // CORRECTION: Gérer le cas où l'email est manquant
            userEmail: user.email || 'unknown',
            userRole: user.role,
            action,
            resource: options.resource,
            resourceId: options.resourceId,
            details: options.details,
            timestamp: firestore_1.FieldValue.serverTimestamp(),
            success: (_a = options.success) !== null && _a !== void 0 ? _a : true,
            errorMessage: options.errorMessage
        };
        await db.collection('auditLogs').add(auditLog);
    }
    catch (error) {
        firebase_functions_1.logger.error('Erreur lors de l\'enregistrement du log d\'audit', {
            userId: user.uid,
            action,
            error: error instanceof Error ? error.message : 'Erreur inconnue'
        });
    }
}
//# sourceMappingURL=audit.js.map