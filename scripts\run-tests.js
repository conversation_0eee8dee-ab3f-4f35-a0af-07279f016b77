/**
 * Script pour exécuter les tests unitaires des Cloud Functions SIGMA
 * Génère un rapport de couverture et valide les seuils
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration des tests
const TEST_CONFIG = {
  // Seuils de couverture requis
  coverageThresholds: {
    global: {
      branches: 80,
      functions: 85,
      lines: 90,
      statements: 90
    },
    dashboard: {
      branches: 85,
      functions: 90,
      lines: 95,
      statements: 95
    }
  },
  
  // Timeout pour les tests
  testTimeout: 30000,
  
  // Répertoires de tests
  testDirs: [
    'src/__tests__/dashboard',
    'src/__tests__/utils'
  ]
};

/**
 * Exécuter une commande shell
 */
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔄 Exécution: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Commande échouée avec le code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Vérifier que les dépendances de test sont installées
 */
async function checkTestDependencies() {
  console.log('📦 Vérification des dépendances de test...');
  
  const packageJsonPath = path.join(process.cwd(), 'functions', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const requiredDevDeps = [
    'jest',
    '@types/jest',
    'firebase-functions-test'
  ];
  
  const missingDeps = requiredDevDeps.filter(dep => 
    !packageJson.devDependencies || !packageJson.devDependencies[dep]
  );
  
  if (missingDeps.length > 0) {
    console.log(`❌ Dépendances manquantes: ${missingDeps.join(', ')}`);
    console.log('💡 Installation des dépendances...');
    
    await runCommand('npm', ['install', '--save-dev', ...missingDeps], {
      cwd: path.join(process.cwd(), 'functions')
    });
  }
  
  console.log('✅ Dépendances de test vérifiées');
}

/**
 * Démarrer l'émulateur Firebase si nécessaire
 */
async function startEmulator() {
  console.log('🔥 Démarrage de l\'émulateur Firebase...');
  
  try {
    // Vérifier si l'émulateur est déjà en cours d'exécution
    await runCommand('curl', ['-s', 'http://localhost:8080'], { stdio: 'ignore' });
    console.log('✅ Émulateur déjà en cours d\'exécution');
    return null;
  } catch (error) {
    // L'émulateur n'est pas en cours d'exécution, le démarrer
    console.log('🚀 Démarrage de l\'émulateur...');
    
    const emulatorProcess = spawn('firebase', ['emulators:start', '--only', 'firestore,auth'], {
      stdio: 'pipe',
      shell: true,
      detached: true
    });
    
    // Attendre que l'émulateur soit prêt
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timeout: émulateur non démarré'));
      }, 30000);
      
      emulatorProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('All emulators ready')) {
          clearTimeout(timeout);
          resolve();
        }
      });
      
      emulatorProcess.stderr.on('data', (data) => {
        console.error('Erreur émulateur:', data.toString());
      });
    });
    
    console.log('✅ Émulateur démarré');
    return emulatorProcess;
  }
}

/**
 * Exécuter les tests Jest
 */
async function runTests() {
  console.log('🧪 Exécution des tests unitaires...');
  
  const jestArgs = [
    'test',
    '--coverage',
    '--coverageReporters=text',
    '--coverageReporters=lcov',
    '--coverageReporters=html',
    '--verbose',
    '--detectOpenHandles',
    '--forceExit'
  ];
  
  // Ajouter les répertoires de tests spécifiques
  TEST_CONFIG.testDirs.forEach(dir => {
    jestArgs.push(`--testPathPattern=${dir}`);
  });
  
  await runCommand('npm', jestArgs, {
    cwd: path.join(process.cwd(), 'functions'),
    env: {
      ...process.env,
      NODE_ENV: 'test',
      FIRESTORE_EMULATOR_HOST: 'localhost:8080',
      FIREBASE_AUTH_EMULATOR_HOST: 'localhost:9099'
    }
  });
  
  console.log('✅ Tests terminés');
}

/**
 * Analyser le rapport de couverture
 */
function analyzeCoverage() {
  console.log('📊 Analyse du rapport de couverture...');
  
  const coverageDir = path.join(process.cwd(), 'functions', 'coverage');
  const lcovPath = path.join(coverageDir, 'lcov.info');
  
  if (!fs.existsSync(lcovPath)) {
    console.log('⚠️ Rapport de couverture non trouvé');
    return;
  }
  
  // Lire le rapport de couverture
  const lcovContent = fs.readFileSync(lcovPath, 'utf8');
  
  // Analyser les métriques par fichier
  const files = lcovContent.split('end_of_record').filter(Boolean);
  const coverage = {};
  
  files.forEach(fileContent => {
    const lines = fileContent.split('\n');
    const sourceFile = lines.find(line => line.startsWith('SF:'));
    
    if (sourceFile) {
      const filePath = sourceFile.replace('SF:', '');
      const fileName = path.basename(filePath);
      
      const linesFound = lines.find(line => line.startsWith('LF:'));
      const linesHit = lines.find(line => line.startsWith('LH:'));
      const functionsFound = lines.find(line => line.startsWith('FNF:'));
      const functionsHit = lines.find(line => line.startsWith('FNH:'));
      const branchesFound = lines.find(line => line.startsWith('BRF:'));
      const branchesHit = lines.find(line => line.startsWith('BRH:'));
      
      if (linesFound && linesHit) {
        const lf = parseInt(linesFound.replace('LF:', ''));
        const lh = parseInt(linesHit.replace('LH:', ''));
        const fnf = functionsFound ? parseInt(functionsFound.replace('FNF:', '')) : 0;
        const fnh = functionsHit ? parseInt(functionsHit.replace('FNH:', '')) : 0;
        const brf = branchesFound ? parseInt(branchesFound.replace('BRF:', '')) : 0;
        const brh = branchesHit ? parseInt(branchesHit.replace('BRH:', '')) : 0;
        
        coverage[fileName] = {
          lines: lf > 0 ? Math.round((lh / lf) * 100) : 100,
          functions: fnf > 0 ? Math.round((fnh / fnf) * 100) : 100,
          branches: brf > 0 ? Math.round((brh / brf) * 100) : 100
        };
      }
    }
  });
  
  // Afficher le résumé
  console.log('\n📈 Résumé de la couverture:');
  console.log('─'.repeat(60));
  
  Object.entries(coverage).forEach(([file, metrics]) => {
    const status = (
      metrics.lines >= TEST_CONFIG.coverageThresholds.global.lines &&
      metrics.functions >= TEST_CONFIG.coverageThresholds.global.functions &&
      metrics.branches >= TEST_CONFIG.coverageThresholds.global.branches
    ) ? '✅' : '❌';
    
    console.log(`${status} ${file}`);
    console.log(`   Lignes: ${metrics.lines}% | Fonctions: ${metrics.functions}% | Branches: ${metrics.branches}%`);
  });
  
  console.log('─'.repeat(60));
  
  // Calculer la couverture globale
  const totalLines = Object.values(coverage).reduce((sum, m) => sum + m.lines, 0);
  const totalFunctions = Object.values(coverage).reduce((sum, m) => sum + m.functions, 0);
  const totalBranches = Object.values(coverage).reduce((sum, m) => sum + m.branches, 0);
  const fileCount = Object.keys(coverage).length;
  
  if (fileCount > 0) {
    const avgLines = Math.round(totalLines / fileCount);
    const avgFunctions = Math.round(totalFunctions / fileCount);
    const avgBranches = Math.round(totalBranches / fileCount);
    
    console.log(`📊 Couverture moyenne:`);
    console.log(`   Lignes: ${avgLines}% (seuil: ${TEST_CONFIG.coverageThresholds.global.lines}%)`);
    console.log(`   Fonctions: ${avgFunctions}% (seuil: ${TEST_CONFIG.coverageThresholds.global.functions}%)`);
    console.log(`   Branches: ${avgBranches}% (seuil: ${TEST_CONFIG.coverageThresholds.global.branches}%)`);
    
    // Vérifier si les seuils sont atteints
    const thresholdsMet = (
      avgLines >= TEST_CONFIG.coverageThresholds.global.lines &&
      avgFunctions >= TEST_CONFIG.coverageThresholds.global.functions &&
      avgBranches >= TEST_CONFIG.coverageThresholds.global.branches
    );
    
    if (thresholdsMet) {
      console.log('✅ Tous les seuils de couverture sont atteints !');
    } else {
      console.log('❌ Certains seuils de couverture ne sont pas atteints');
    }
  }
  
  console.log(`\n💡 Rapport HTML disponible: functions/coverage/index.html`);
}

/**
 * Nettoyer les ressources
 */
async function cleanup(emulatorProcess) {
  if (emulatorProcess) {
    console.log('🧹 Arrêt de l\'émulateur...');
    emulatorProcess.kill('SIGTERM');
    
    // Attendre un peu pour que l'émulateur s'arrête proprement
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 EXÉCUTION DES TESTS UNITAIRES SIGMA DASHBOARD');
  console.log('='.repeat(60));
  
  let emulatorProcess = null;
  
  try {
    // 1. Vérifier les dépendances
    await checkTestDependencies();
    
    // 2. Démarrer l'émulateur
    emulatorProcess = await startEmulator();
    
    // 3. Exécuter les tests
    await runTests();
    
    // 4. Analyser la couverture
    analyzeCoverage();
    
    console.log('\n✅ TESTS TERMINÉS AVEC SUCCÈS');
    
  } catch (error) {
    console.error('\n❌ ERREUR LORS DES TESTS:', error.message);
    process.exit(1);
    
  } finally {
    // 5. Nettoyer
    await cleanup(emulatorProcess);
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  main();
}

module.exports = {
  runTests,
  analyzeCoverage,
  TEST_CONFIG
};
