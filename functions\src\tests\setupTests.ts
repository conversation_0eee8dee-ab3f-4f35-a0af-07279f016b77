// Configuration globale pour les tests Jest
import { getApps, initializeApp } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";

// Configuration des émulateurs pour les tests
process.env.FIRESTORE_EMULATOR_HOST = "localhost:8080";
process.env.FIREBASE_AUTH_EMULATOR_HOST = "localhost:9099";

// Initialiser Firebase Admin SDK pour les tests uniquement si ce n'est pas déjà fait
if (!getApps().length) {
  initializeApp({
    projectId: "sigma-nova", // Utiliser le même projectId que l'émulateur
    // Pas besoin de credentials pour les émulateurs
  });
}

// Exporter les instances pour les tests
export const adminAuth = getAuth();
export const adminDb = getFirestore();

// Nettoyage après tous les tests
afterAll(async () => {
  // Les émulateurs seront nettoyés par firebase-functions-test
});
