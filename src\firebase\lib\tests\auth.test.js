"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Tests unitaires pour les Cloud Functions d'authentification (émulateurs).
 * - Aligne la région client (europe-west1) avec la région Functions
 * - Connecte l'instance regionnée au Functions Emulator
 * - Corrige l'assertion non-admin (code 'functions/permission-denied')
 * - Rend onUserCreate robuste via polling
 */
const globals_1 = require("@jest/globals");
const rules_unit_testing_1 = require("@firebase/rules-unit-testing");
const auth_1 = require("firebase-admin/auth");
const firestore_1 = require("firebase-admin/firestore");
const functions_1 = require("firebase/functions");
const app_1 = require("firebase/app");
const auth_2 = require("firebase/auth");
const PROJECT_ID = process.env.GCLOUD_PROJECT || 'sigma-nova';
let testEnv;
let adminAuth;
let adminFirestore;
let testApp;
const testUsers = {
    admin: { uid: 'test-admin-uid', email: '<EMAIL>', displayName: 'Admin', role: 'admin' },
    user: { uid: 'test-user-uid', email: '<EMAIL>', displayName: 'User', role: 'utilisateur' },
};
(0, globals_1.beforeAll)(async () => {
    testEnv = await (0, rules_unit_testing_1.initializeTestEnvironment)({
        projectId: PROJECT_ID,
        firestore: {
            host: '127.0.0.1',
            port: 8080,
            rules: `
        rules_version = '2';
        service cloud.firestore {
          match /databases/{database}/documents {
            match /{document=**} {
              allow read, write: if true; // tests uniquement
            }
          }
        }
      `,
        },
    });
    adminAuth = (0, auth_1.getAuth)();
    adminFirestore = (0, firestore_1.getFirestore)();
    // Nettoyer d'anciennes apps client éventuelles
    (0, app_1.getApps)().forEach(app => (0, app_1.deleteApp)(app));
    testApp = (0, app_1.initializeApp)({
        apiKey: 'fake',
        projectId: PROJECT_ID,
        authDomain: `${PROJECT_ID}.firebaseapp.com`,
        storageBucket: `${PROJECT_ID}.appspot.com`,
    });
    (0, auth_2.connectAuthEmulator)((0, auth_2.getAuth)(testApp), 'http://127.0.0.1:9099', { disableWarnings: true });
}, 30000);
(0, globals_1.afterAll)(async () => {
    await testEnv.cleanup();
    const apps = (0, app_1.getApps)();
    await Promise.all(apps.map(app => (0, app_1.deleteApp)(app)));
}, 30000);
(0, globals_1.beforeEach)(async () => {
    // Reset Firestore
    await testEnv.clearFirestore();
    // (Ré)initialisation idempotente des utilisateurs dans l'émulateur Auth
    async function ensureUser(uid, email, displayName) {
        var _a;
        try {
            await adminAuth.createUser({ uid, email, displayName });
        }
        catch (e) {
            // Ignore si déjà existant
            if (((_a = e === null || e === void 0 ? void 0 : e.errorInfo) === null || _a === void 0 ? void 0 : _a.code) !== 'auth/uid-already-exists')
                throw e;
        }
    }
    await Promise.all([
        ensureUser(testUsers.admin.uid, testUsers.admin.email, testUsers.admin.displayName),
        ensureUser(testUsers.user.uid, testUsers.user.email, testUsers.user.displayName),
    ]);
    // Claims initiaux
    await adminAuth.setCustomUserClaims(testUsers.admin.uid, { role: testUsers.admin.role });
    await adminAuth.setCustomUserClaims(testUsers.user.uid, { role: testUsers.user.role });
    // Docs Firestore initiaux (sauf pour le futur "new user")
    await adminFirestore.collection('users').doc(testUsers.admin.uid).set({
        email: testUsers.admin.email, displayName: testUsers.admin.displayName, role: testUsers.admin.role, createdAt: new Date(),
    });
    await adminFirestore.collection('users').doc(testUsers.user.uid).set({
        email: testUsers.user.email, displayName: testUsers.user.displayName, role: testUsers.user.role, createdAt: new Date(),
    });
}, 30000);
// ---- Helper : instance Functions authentifiée, regionnée et connectée
async function getAuthenticatedFunctions(user) {
    const auth = (0, auth_2.getAuth)(testApp);
    const token = await adminAuth.createCustomToken(user.uid, user.role ? { role: user.role } : undefined);
    await (0, auth_2.signInWithCustomToken)(auth, token);
    const funcs = (0, functions_1.getFunctions)(testApp, 'europe-west1');
    (0, functions_1.connectFunctionsEmulator)(funcs, '127.0.0.1', 5001);
    return funcs;
}
(0, globals_1.describe)("Cloud Functions d'Authentification", () => {
    (0, globals_1.describe)('setUserRole', () => {
        (0, globals_1.it)("devrait permettre à un admin d'assigner un rôle", async () => {
            var _a;
            const functions = await getAuthenticatedFunctions(testUsers.admin);
            const setUserRole = (0, functions_1.httpsCallable)(functions, 'setUserRole');
            const res = await setUserRole({ userId: testUsers.user.uid, role: 'regisseur' });
            const data = res.data;
            (0, globals_1.expect)(data === null || data === void 0 ? void 0 : data.success).toBe(true);
            (0, globals_1.expect)(data === null || data === void 0 ? void 0 : data.newRole).toBe('regisseur');
            const updated = await adminAuth.getUser(testUsers.user.uid);
            (0, globals_1.expect)((_a = updated.customClaims) === null || _a === void 0 ? void 0 : _a.role).toBe('regisseur');
        }, 30000);
        (0, globals_1.it)("devrait refuser l'accès à un non-admin", async () => {
            const functions = await getAuthenticatedFunctions(testUsers.user);
            const setUserRole = (0, functions_1.httpsCallable)(functions, 'setUserRole');
            await (0, globals_1.expect)(setUserRole({ userId: testUsers.admin.uid, role: 'utilisateur' }))
                .rejects.toMatchObject({ code: 'functions/permission-denied' }); // vérifie le code
        }, 30000);
    });
    (0, globals_1.describe)('onUserCreate', () => {
        (0, globals_1.it)('devrait assigner le rôle utilisateur par défaut', async () => {
            var _a, _b;
            // uid unique par run
            const NEW_UID = `test-new-user-uid-${Date.now()}`;
            await adminAuth.createUser({ uid: NEW_UID, email: '<EMAIL>' });
            // Polling pour laisser le trigger finir : claims + doc Firestore
            const deadline = Date.now() + 10000;
            let role;
            let hasDoc = false;
            while (Date.now() < deadline) {
                const rec = await adminAuth.getUser(NEW_UID).catch(() => null);
                role = (_a = rec === null || rec === void 0 ? void 0 : rec.customClaims) === null || _a === void 0 ? void 0 : _a.role;
                const doc = await adminFirestore.collection('users').doc(NEW_UID).get();
                hasDoc = doc.exists && ((_b = doc.data()) === null || _b === void 0 ? void 0 : _b.role) === 'utilisateur';
                if (role === 'utilisateur' && hasDoc)
                    break;
                await new Promise(r => setTimeout(r, 200));
            }
            (0, globals_1.expect)(role).toBe('utilisateur');
            (0, globals_1.expect)(hasDoc).toBe(true);
        }, 30000);
    });
});
//# sourceMappingURL=auth.test.js.map