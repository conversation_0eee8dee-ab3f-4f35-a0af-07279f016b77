/**
 * Configuration commune des tests : variables d'émulateur + init Admin SDK.
 * Chargé par les tests d'authentification.
 */
process.env.FIRESTORE_EMULATOR_HOST ??= "127.0.0.1:8080";
process.env.FIREBASE_AUTH_EMULATOR_HOST ??= "127.0.0.1:9099";

import { getApps, initializeApp } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";

// Initialise l'app Admin pour permettre getAuth()/getFirestore()
if (!getApps().length) {
  initializeApp({ projectId: "sigma-nova" });
}

/** Helpers utilisés par les tests */
export const getAdminAuth = () => getAuth();
export const getAdminFirestore = () => getFirestore();
