/**
 * Tests de sécurité pour les règles Firestore SIGMA
 * Valide tous les scénarios d'accès autorisé/non autorisé selon les rôles
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, updateDoc, deleteDoc, collection, addDoc, query, where, getDocs } = require('firebase/firestore');

// Configuration des tests
const PROJECT_ID = 'sigma-firestore-rules-test';

describe('Tests de Sécurité Firestore - Règles SIGMA', () => {
  let testEnv;

  // Utilisateurs de test avec différents rôles
  const users = {
    admin: { uid: 'admin-uid', role: 'admin', email: '<EMAIL>' },
    regisseur: { uid: 'regisseur-uid', role: 'regisseur', email: '<EMAIL>' },
    utilisateur: { uid: 'user-uid', role: 'utilisateur', email: '<EMAIL>' },
    otherUser: { uid: 'other-uid', role: 'utilisateur', email: '<EMAIL>' },
    anonymous: null
  };

  beforeAll(async () => {
    testEnv = await initializeTestEnvironment({
      projectId: PROJECT_ID,
      firestore: {
        rules: require('fs').readFileSync('firestore.rules', 'utf8'),
        host: 'localhost',
        port: 8080
      }
    });
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  beforeEach(async () => {
    await testEnv.clearFirestore();
  });

  /**
   * Utilitaire pour créer un contexte d'authentification
   */
  function getAuthContext(user) {
    if (!user) return testEnv.unauthenticatedContext();
    return testEnv.authenticatedContext(user.uid, { role: user.role, email: user.email });
  }

  /**
   * Utilitaire pour créer des données de test
   */
  function createTestData() {
    return {
      emprunt: {
        nom: 'Test Emprunt',
        lieu: 'Test Lieu',
        dateDepart: '2024-12-01T10:00:00Z',
        dateRetour: '2024-12-08T10:00:00Z',
        emprunteur: 'Test Emprunteur',
        statut: 'Pas prêt',
        createdBy: users.utilisateur.uid,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      stock: {
        nom: 'Test Stock',
        description: 'Description test',
        categorie: 'Test',
        quantite: 10,
        seuil: 2,
        unite: 'pièce',
        estActif: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      module: {
        nom: 'Test Module',
        description: 'Description module',
        categorie: 'Test',
        contenu: [{ stockId: 'stock-1', nom: 'Item', quantite: 1 }],
        estPret: true,
        estActif: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      livraison: {
        empruntRef: 'emprunt-1',
        type: 'aller',
        datePrevu: '2024-12-01T10:00:00Z',
        adresse: {
          rue: '123 Test Street',
          ville: 'Test City',
          codePostal: '12345'
        },
        contact: {
          nom: 'Test Contact',
          telephone: '0123456789'
        },
        statut: 'planifiee',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      user: {
        email: users.utilisateur.email,
        displayName: 'Test User',
        role: 'utilisateur',
        isActive: true,
        createdAt: new Date(),
        preferences: {
          language: 'fr',
          theme: 'light'
        }
      }
    };
  }

  describe('Collection: users', () => {
    it('Admin peut lire tous les utilisateurs', async () => {
      const adminDb = getAuthContext(users.admin).firestore();
      const testData = createTestData();
      
      // Créer un utilisateur de test
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', users.utilisateur.uid), testData.user);
      });

      await assertSucceeds(getDoc(doc(adminDb, 'users', users.utilisateur.uid)));
    });

    it('Utilisateur peut lire son propre profil', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', users.utilisateur.uid), testData.user);
      });

      await assertSucceeds(getDoc(doc(userDb, 'users', users.utilisateur.uid)));
    });

    it('Utilisateur ne peut pas lire le profil d\'un autre utilisateur', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', users.otherUser.uid), testData.user);
      });

      await assertFails(getDoc(doc(userDb, 'users', users.otherUser.uid)));
    });

    it('Utilisateur anonyme ne peut pas accéder aux profils', async () => {
      const anonDb = getAuthContext(users.anonymous).firestore();
      
      await assertFails(getDoc(doc(anonDb, 'users', users.utilisateur.uid)));
    });

    it('Admin peut modifier n\'importe quel profil', async () => {
      const adminDb = getAuthContext(users.admin).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', users.utilisateur.uid), testData.user);
      });

      await assertSucceeds(updateDoc(doc(adminDb, 'users', users.utilisateur.uid), {
        displayName: 'Nouveau nom'
      }));
    });

    it('Utilisateur peut modifier son propre profil', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', users.utilisateur.uid), testData.user);
      });

      await assertSucceeds(updateDoc(doc(userDb, 'users', users.utilisateur.uid), {
        displayName: 'Mon nouveau nom'
      }));
    });

    it('Utilisateur ne peut pas modifier le rôle dans son profil', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'users', users.utilisateur.uid), testData.user);
      });

      await assertFails(updateDoc(doc(userDb, 'users', users.utilisateur.uid), {
        role: 'admin'
      }));
    });
  });

  describe('Collection: emprunts', () => {
    it('Tous les utilisateurs authentifiés peuvent lire les emprunts', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'emprunts', 'emprunt-1'), testData.emprunt);
      });

      await assertSucceeds(getDoc(doc(userDb, 'emprunts', 'emprunt-1')));
    });

    it('Utilisateur anonyme ne peut pas lire les emprunts', async () => {
      const anonDb = getAuthContext(users.anonymous).firestore();
      
      await assertFails(getDoc(doc(anonDb, 'emprunts', 'emprunt-1')));
    });

    it('Utilisateur peut créer un emprunt', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await assertSucceeds(addDoc(collection(userDb, 'emprunts'), testData.emprunt));
    });

    it('Propriétaire peut modifier son emprunt', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'emprunts', 'emprunt-1'), testData.emprunt);
      });

      await assertSucceeds(updateDoc(doc(userDb, 'emprunts', 'emprunt-1'), {
        nom: 'Emprunt modifié'
      }));
    });

    it('Non-propriétaire ne peut pas modifier un emprunt (sauf admin/régisseur)', async () => {
      const otherUserDb = getAuthContext(users.otherUser).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'emprunts', 'emprunt-1'), testData.emprunt);
      });

      await assertFails(updateDoc(doc(otherUserDb, 'emprunts', 'emprunt-1'), {
        nom: 'Tentative modification'
      }));
    });

    it('Régisseur peut modifier n\'importe quel emprunt', async () => {
      const regisseurDb = getAuthContext(users.regisseur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'emprunts', 'emprunt-1'), testData.emprunt);
      });

      await assertSucceeds(updateDoc(doc(regisseurDb, 'emprunts', 'emprunt-1'), {
        statut: 'Prêt'
      }));
    });

    it('Seuls admin/régisseur peuvent supprimer un emprunt', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const adminDb = getAuthContext(users.admin).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'emprunts', 'emprunt-1'), testData.emprunt);
      });

      // Utilisateur normal ne peut pas supprimer
      await assertFails(deleteDoc(doc(userDb, 'emprunts', 'emprunt-1')));
      
      // Admin peut supprimer
      await assertSucceeds(deleteDoc(doc(adminDb, 'emprunts', 'emprunt-1')));
    });
  });

  describe('Collection: stocks', () => {
    it('Tous les utilisateurs authentifiés peuvent lire les stocks', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'stocks', 'stock-1'), testData.stock);
      });

      await assertSucceeds(getDoc(doc(userDb, 'stocks', 'stock-1')));
    });

    it('Seuls admin/régisseur peuvent modifier les stocks', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const regisseurDb = getAuthContext(users.regisseur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'stocks', 'stock-1'), testData.stock);
      });

      // Utilisateur normal ne peut pas modifier
      await assertFails(updateDoc(doc(userDb, 'stocks', 'stock-1'), {
        quantite: 5
      }));
      
      // Régisseur peut modifier
      await assertSucceeds(updateDoc(doc(regisseurDb, 'stocks', 'stock-1'), {
        quantite: 5
      }));
    });

    it('Seuls admin/régisseur peuvent créer des stocks', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const adminDb = getAuthContext(users.admin).firestore();
      const testData = createTestData();
      
      // Utilisateur normal ne peut pas créer
      await assertFails(addDoc(collection(userDb, 'stocks'), testData.stock));
      
      // Admin peut créer
      await assertSucceeds(addDoc(collection(adminDb, 'stocks'), testData.stock));
    });
  });

  describe('Collection: modules', () => {
    it('Tous les utilisateurs authentifiés peuvent lire les modules', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'modules', 'module-1'), testData.module);
      });

      await assertSucceeds(getDoc(doc(userDb, 'modules', 'module-1')));
    });

    it('Seuls admin/régisseur peuvent modifier les modules', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const adminDb = getAuthContext(users.admin).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'modules', 'module-1'), testData.module);
      });

      // Utilisateur normal ne peut pas modifier
      await assertFails(updateDoc(doc(userDb, 'modules', 'module-1'), {
        estPret: false
      }));
      
      // Admin peut modifier
      await assertSucceeds(updateDoc(doc(adminDb, 'modules', 'module-1'), {
        estPret: false
      }));
    });
  });

  describe('Collection: livraisons', () => {
    it('Tous les utilisateurs authentifiés peuvent lire les livraisons', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'livraisons', 'livraison-1'), testData.livraison);
      });

      await assertSucceeds(getDoc(doc(userDb, 'livraisons', 'livraison-1')));
    });

    it('Seuls admin/régisseur peuvent modifier les livraisons', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const regisseurDb = getAuthContext(users.regisseur).firestore();
      const testData = createTestData();
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'livraisons', 'livraison-1'), testData.livraison);
      });

      // Utilisateur normal ne peut pas modifier
      await assertFails(updateDoc(doc(userDb, 'livraisons', 'livraison-1'), {
        statut: 'en_cours'
      }));
      
      // Régisseur peut modifier
      await assertSucceeds(updateDoc(doc(regisseurDb, 'livraisons', 'livraison-1'), {
        statut: 'en_cours'
      }));
    });
  });

  describe('Collection: roleHistory', () => {
    it('Seuls les admins peuvent lire l\'historique des rôles', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const adminDb = getAuthContext(users.admin).firestore();
      
      const roleHistoryData = {
        userId: users.utilisateur.uid,
        previousRole: 'utilisateur',
        newRole: 'regisseur',
        changedBy: users.admin.uid,
        changedAt: new Date(),
        reason: 'Promotion'
      };
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'roleHistory', 'history-1'), roleHistoryData);
      });

      // Utilisateur normal ne peut pas lire
      await assertFails(getDoc(doc(userDb, 'roleHistory', 'history-1')));
      
      // Admin peut lire
      await assertSucceeds(getDoc(doc(adminDb, 'roleHistory', 'history-1')));
    });

    it('Seuls les admins peuvent écrire dans l\'historique des rôles', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const adminDb = getAuthContext(users.admin).firestore();
      
      const roleHistoryData = {
        userId: users.utilisateur.uid,
        previousRole: 'utilisateur',
        newRole: 'regisseur',
        changedBy: users.admin.uid,
        changedAt: new Date(),
        reason: 'Promotion'
      };

      // Utilisateur normal ne peut pas écrire
      await assertFails(addDoc(collection(userDb, 'roleHistory'), roleHistoryData));
      
      // Admin peut écrire
      await assertSucceeds(addDoc(collection(adminDb, 'roleHistory'), roleHistoryData));
    });
  });

  describe('Collection: auditLogs', () => {
    it('Seuls les admins peuvent lire les logs d\'audit', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      const adminDb = getAuthContext(users.admin).firestore();
      
      const auditLogData = {
        userId: users.utilisateur.uid,
        action: 'emprunt.create',
        timestamp: new Date(),
        success: true,
        details: { empruntId: 'emprunt-1' }
      };
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'auditLogs', 'log-1'), auditLogData);
      });

      // Utilisateur normal ne peut pas lire
      await assertFails(getDoc(doc(userDb, 'auditLogs', 'log-1')));
      
      // Admin peut lire
      await assertSucceeds(getDoc(doc(adminDb, 'auditLogs', 'log-1')));
    });

    it('Personne ne peut modifier les logs d\'audit', async () => {
      const adminDb = getAuthContext(users.admin).firestore();
      
      const auditLogData = {
        userId: users.utilisateur.uid,
        action: 'emprunt.create',
        timestamp: new Date(),
        success: true
      };
      
      await testEnv.withSecurityRulesDisabled(async (context) => {
        await setDoc(doc(context.firestore(), 'auditLogs', 'log-1'), auditLogData);
      });

      // Même l'admin ne peut pas modifier les logs
      await assertFails(updateDoc(doc(adminDb, 'auditLogs', 'log-1'), {
        success: false
      }));
    });
  });

  describe('Validation des données', () => {
    it('Emprunt doit avoir les champs requis', async () => {
      const userDb = getAuthContext(users.utilisateur).firestore();
      
      // Emprunt sans nom (requis)
      await assertFails(addDoc(collection(userDb, 'emprunts'), {
        lieu: 'Test Lieu',
        dateDepart: '2024-12-01T10:00:00Z',
        dateRetour: '2024-12-08T10:00:00Z',
        emprunteur: 'Test Emprunteur',
        statut: 'Pas prêt'
      }));
    });

    it('Stock doit avoir une quantité positive', async () => {
      const adminDb = getAuthContext(users.admin).firestore();
      
      // Stock avec quantité négative
      await assertFails(addDoc(collection(adminDb, 'stocks'), {
        nom: 'Test Stock',
        categorie: 'Test',
        quantite: -5, // Invalide
        seuil: 2,
        unite: 'pièce',
        estActif: true
      }));
    });

    it('Utilisateur ne peut pas définir un rôle invalide', async () => {
      const adminDb = getAuthContext(users.admin).firestore();
      
      // Rôle invalide
      await assertFails(setDoc(doc(adminDb, 'users', 'new-user'), {
        email: '<EMAIL>',
        displayName: 'Test User',
        role: 'super-admin', // Rôle invalide
        isActive: true
      }));
    });
  });
});
