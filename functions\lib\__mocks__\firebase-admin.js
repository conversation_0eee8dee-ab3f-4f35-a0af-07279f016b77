"use strict";
// Créez un mock pour firebase-admin
const mockAuth = {
    createUser: jest.fn(),
    deleteUser: jest.fn(),
    getUserByEmail: jest.fn(),
    getUser: jest.fn(),
    setCustomUserClaims: jest.fn(),
};
const mockFirestore = {
    runTransaction: jest.fn(),
    collection: jest.fn(() => ({
        doc: jest.fn(() => ({
            get: jest.fn(),
            set: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
        })),
        add: jest.fn(),
    })),
};
const mockAdmin = {
    auth: jest.fn(() => mockAuth),
    firestore: jest.fn(() => mockFirestore),
    initializeApp: jest.fn(),
    credential: {
        cert: jest.fn(),
    },
};
module.exports = mockAdmin;
