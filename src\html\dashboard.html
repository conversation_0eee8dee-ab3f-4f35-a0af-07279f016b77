<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard SIGMA - Gestion Logistique</title>
    
    <!-- Styles CSS -->
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/common.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-functions-compat.js"></script>
    
    <!-- Configuration Firebase -->
    <script src="../js/firebase-config.js"></script>
</head>
<body>
    <!-- Header principal -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="header-left">
                <h1>📊 Dashboard SIGMA</h1>
                <div class="refresh-indicator" id="refresh-indicator">
                    🔄 Initialisation...
                </div>
            </div>
            <div class="header-right">
                <div class="performance-metrics" id="performance-metrics"></div>
                <div class="user-info" id="user-info">
                    <span id="user-name">Chargement...</span>
                    <button class="btn btn-outline" onclick="logout()">Déconnexion</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Container principal -->
    <main class="dashboard-container" id="dashboard-container">
        <!-- Conteneur d'erreurs -->
        <div class="error-container" id="error-container"></div>
        
        <!-- Grille des tableaux dashboard -->
        <div class="dashboard-grid">
            
            <!-- Tableau 1: Alertes Stock -->
            <div class="dashboard-card critical" id="stock-alerts">
                <div class="card-header">
                    <h3>🚨 Alertes Stock</h3>
                    <div class="card-actions">
                        <button class="btn-icon" onclick="dashboardManager.refreshTable('stockAlerts')" title="Actualiser">
                            🔄
                        </button>
                        <button class="btn-icon" onclick="dashboardUI.exportTable('stockAlerts')" title="Exporter">
                            📊
                        </button>
                    </div>
                </div>
                <div class="table-container" id="stock-alerts-table">
                    <div class="loading-placeholder">
                        <div class="spinner"></div>
                        <span>Chargement des alertes stock...</span>
                    </div>
                </div>
                <div class="table-stats" id="stock-alerts-stats"></div>
            </div>

            <!-- Tableau 2: Matériel Manquant -->
            <div class="dashboard-card warning" id="missing-material">
                <div class="card-header">
                    <h3>❌ Matériel Manquant</h3>
                    <div class="card-actions">
                        <button class="btn-icon" onclick="dashboardManager.refreshTable('missingMaterial')" title="Actualiser">
                            🔄
                        </button>
                        <button class="btn-icon" onclick="dashboardUI.exportTable('missingMaterial')" title="Exporter">
                            📊
                        </button>
                    </div>
                </div>
                <div class="table-container" id="missing-material-table">
                    <div class="loading-placeholder">
                        <div class="spinner"></div>
                        <span>Chargement du matériel manquant...</span>
                    </div>
                </div>
                <div class="table-stats" id="missing-material-stats"></div>
            </div>

            <!-- Tableau 3: Emprunts en Retard -->
            <div class="dashboard-card critical" id="overdue-emprunts">
                <div class="card-header">
                    <h3>⏰ Emprunts en Retard</h3>
                    <div class="card-actions">
                        <button class="btn-icon" onclick="dashboardManager.refreshTable('overdueEmprunts')" title="Actualiser">
                            🔄
                        </button>
                        <button class="btn-icon" onclick="dashboardUI.exportTable('overdueEmprunts')" title="Exporter">
                            📊
                        </button>
                    </div>
                </div>
                <div class="table-container" id="overdue-emprunts-table">
                    <div class="loading-placeholder">
                        <div class="spinner"></div>
                        <span>Chargement des emprunts en retard...</span>
                    </div>
                </div>
                <div class="table-stats" id="overdue-emprunts-stats"></div>
            </div>

            <!-- Tableau 4: Prochains Emprunts -->
            <div class="dashboard-card info" id="upcoming-emprunts">
                <div class="card-header">
                    <h3>📅 Prochains Emprunts</h3>
                    <div class="card-actions">
                        <button class="btn-icon" onclick="dashboardManager.refreshTable('upcomingEmprunts')" title="Actualiser">
                            🔄
                        </button>
                        <button class="btn-icon" onclick="dashboardUI.exportTable('upcomingEmprunts')" title="Exporter">
                            📊
                        </button>
                    </div>
                </div>
                <div class="table-container" id="upcoming-emprunts-table">
                    <div class="loading-placeholder">
                        <div class="spinner"></div>
                        <span>Chargement des prochains emprunts...</span>
                    </div>
                </div>
                <div class="table-stats" id="upcoming-emprunts-stats"></div>
            </div>

            <!-- Tableau 5: Modules Non Opérationnels -->
            <div class="dashboard-card warning" id="non-op-modules">
                <div class="card-header">
                    <h3>📌 Modules Non Opérationnels</h3>
                    <div class="card-actions">
                        <button class="btn-icon" onclick="dashboardManager.refreshTable('nonOpModules')" title="Actualiser">
                            🔄
                        </button>
                        <button class="btn-icon" onclick="dashboardUI.exportTable('nonOpModules')" title="Exporter">
                            📊
                        </button>
                    </div>
                </div>
                <div class="table-container" id="non-op-modules-table">
                    <div class="loading-placeholder">
                        <div class="spinner"></div>
                        <span>Chargement des modules...</span>
                    </div>
                </div>
                <div class="table-stats" id="non-op-modules-stats"></div>
            </div>

            <!-- Tableau 6: Matériel Non Opérationnel -->
            <div class="dashboard-card warning" id="non-op-material">
                <div class="card-header">
                    <h3>🔧 Matériel Non Opérationnel</h3>
                    <div class="card-actions">
                        <button class="btn-icon" onclick="dashboardManager.refreshTable('nonOpMaterial')" title="Actualiser">
                            🔄
                        </button>
                        <button class="btn-icon" onclick="dashboardUI.exportTable('nonOpMaterial')" title="Exporter">
                            📊
                        </button>
                    </div>
                </div>
                <div class="table-container" id="non-op-material-table">
                    <div class="loading-placeholder">
                        <div class="spinner"></div>
                        <span>Chargement du matériel...</span>
                    </div>
                </div>
                <div class="table-stats" id="non-op-material-stats"></div>
            </div>

            <!-- Tableau 7: Emprunts en Attente -->
            <div class="dashboard-card info" id="pending-emprunts">
                <div class="card-header">
                    <h3>📋 Emprunts en Attente</h3>
                    <div class="card-actions">
                        <button class="btn-icon" onclick="dashboardManager.refreshTable('pendingEmprunts')" title="Actualiser">
                            🔄
                        </button>
                        <button class="btn-icon" onclick="dashboardUI.exportTable('pendingEmprunts')" title="Exporter">
                            📊
                        </button>
                    </div>
                </div>
                <div class="table-container" id="pending-emprunts-table">
                    <div class="loading-placeholder">
                        <div class="spinner"></div>
                        <span>Chargement des emprunts en attente...</span>
                    </div>
                </div>
                <div class="table-stats" id="pending-emprunts-stats"></div>
            </div>

        </div>
    </main>

    <!-- Footer -->
    <footer class="dashboard-footer">
        <div class="footer-content">
            <div class="footer-left">
                <span>© 2024 SIGMA - Dashboard Logistique</span>
            </div>
            <div class="footer-right">
                <span id="last-update">Dernière mise à jour: --:--</span>
                <button class="btn btn-sm btn-outline" onclick="refreshDashboard()">
                    🔄 Actualiser tout
                </button>
            </div>
        </div>
    </footer>

    <!-- Scripts JavaScript -->
    <script src="../js/auth.js"></script>
    <script src="../js/dashboard/PaginationManager.js"></script>
    <script src="../js/dashboard/DashboardUI.js"></script>
    <script src="../js/dashboard/DashboardManager.js"></script>
    <script src="../js/dashboard/dashboard-init.js"></script>

    <!-- Script d'initialisation -->
    <script>
        // Fonction de déconnexion
        function logout() {
            firebase.auth().signOut().then(() => {
                window.location.href = 'login.html';
            }).catch((error) => {
                console.error('Erreur lors de la déconnexion:', error);
            });
        }

        // Mise à jour de l'horodatage
        function updateLastUpdateTime() {
            const lastUpdateElement = document.getElementById('last-update');
            if (lastUpdateElement) {
                lastUpdateElement.textContent = `Dernière mise à jour: ${new Date().toLocaleTimeString('fr-FR')}`;
            }
        }

        // Mettre à jour l'horodatage toutes les minutes
        setInterval(updateLastUpdateTime, 60000);
        updateLastUpdateTime();
    </script>
</body>
</html>
